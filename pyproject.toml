[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "intensification"
version = "2.0.0"
description = "Measles transmission modeling and vaccination campaign analysis for Southern Nigeria"
readme = "README_new.md"
license = {file = "LICENSE"}
authors = [
    {name = "Research Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Research Team", email = "<EMAIL>"}
]
keywords = [
    "epidemiology",
    "measles",
    "transmission-modeling", 
    "vaccination",
    "nigeria",
    "sir-model",
    "public-health"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
    "Topic :: Scientific/Engineering :: Mathematics",
]
requires-python = ">=3.12"
dependencies = [
    "numpy>=1.24.0",
    "scipy>=1.10.0", 
    "pandas>=2.0.0",
    "matplotlib>=3.7.0",
    "scikit-learn>=1.3.0",
    "seaborn>=0.12.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0",
    "tqdm>=4.65.0",
    "pydantic>=2.0.0",
    "typer>=0.9.0",
    "rich>=13.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0"
]
docs = [
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0",
    "sphinx-autodoc-typehints>=1.20.0"
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "hypothesis>=6.70.0"
]

[project.urls]
Homepage = "https://github.com/NThakkar-IDM/intensification"
Documentation = "https://intensification.readthedocs.io/"
Repository = "https://github.com/NThakkar-IDM/intensification.git"
"Bug Tracker" = "https://github.com/NThakkar-IDM/intensification/issues"

[project.scripts]
intensification = "src.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.setuptools.package-dir]
"" = "."

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
