# API Reference - Intensification Package

This document provides comprehensive API reference for the modernized intensification package.

## Core Module (`src.core`)

### NeighborhoodSIRModel

The main transmission model class implementing the neighborhood SIR approach.

```python
class NeighborhoodSIRModel(BaseTransmissionModel):
    """
    Neighborhood SIR transmission model with spatial correlation.
    
    This model implements a stochastic SIR transmission model that incorporates:
    - Spatial neighborhood effects for seasonality correlation
    - Vaccination campaign (SIA) effects  
    - Birth and demographic processes
    - Under-reporting of cases
    """
```

#### Methods

##### `__init__(parameters: Optional[ModelParameters] = None)`
Initialize the Neighborhood SIR model.

**Parameters:**
- `parameters`: Model parameters. If None, uses default parameters.

##### `fit(data: pd.DataFrame, sia_effects: np.ndarray, s0_prior: float, s0_var: float, **kwargs) -> ModelResults`
Fit the neighborhood SIR model to epidemiological data.

**Parameters:**
- `data`: DataFrame with columns ['cases', 'adj_births', 'adj_cases_p']
- `sia_effects`: Array of SIA (vaccination campaign) effects
- `s0_prior`: Prior estimate of initial susceptible population
- `s0_var`: Variance of initial susceptible population prior
- `**kwargs`: Additional fitting parameters

**Returns:**
- `ModelResults`: Object containing fitting results

##### `predict(time_points: np.ndarray, **kwargs) -> np.ndarray`
Generate predictions from the fitted model.

**Parameters:**
- `time_points`: Time points for prediction
- `**kwargs`: Additional prediction parameters

**Returns:**
- `np.ndarray`: Array of predicted case counts

##### `log_likelihood(theta: np.ndarray) -> float`
Compute the log-likelihood for given parameters.

**Parameters:**
- `theta`: Parameter vector [log_S0, mu_1, mu_2, ..., mu_n]

**Returns:**
- `float`: Log-likelihood value

### SurvivalAnalyzer

Survival analysis for vaccination coverage and immunity estimation.

```python
class SurvivalAnalyzer:
    """
    Survival analysis for estimating vaccination coverage and immunity gaps.
    
    This class implements the survival-based approach described in Appendix 2
    of the research paper to partition birth cohorts by immunity source and
    estimate the fraction left to infection.
    """
```

#### Methods

##### `add_vaccination_opportunity(opportunity: VaccinationOpportunity) -> None`
Add a vaccination opportunity to the analysis.

**Parameters:**
- `opportunity`: VaccinationOpportunity object

##### `analyze_cohort(birth_year: int, cohort_size: int) -> CohortAnalysisResult`
Perform survival analysis for a single birth cohort.

**Parameters:**
- `birth_year`: Year of birth for the cohort
- `cohort_size`: Size of the birth cohort

**Returns:**
- `CohortAnalysisResult`: Immunity partitioning results

##### `estimate_annual_burden(cohort_results: List[CohortAnalysisResult]) -> pd.DataFrame`
Estimate expected annual burden from cohort analysis results.

**Parameters:**
- `cohort_results`: List of cohort analysis results

**Returns:**
- `pd.DataFrame`: Annual burden estimates

### Configuration Classes

#### `Config`
Main configuration class combining all settings.

```python
class Config(BaseModel):
    """Main configuration class combining all settings."""
    
    data_paths: DataPaths = Field(default_factory=DataPaths)
    model: ModelConfig = Field(default_factory=ModelConfig)
    analysis: AnalysisConfig = Field(default_factory=AnalysisConfig)
```

##### Class Methods

##### `from_file(config_path: Path) -> "Config"`
Load configuration from file.

##### `to_file(config_path: Path) -> None`
Save configuration to file.

## Data Module (`src.data`)

### DataLoader

Centralized data loader for all epidemiological and demographic data.

```python
class DataLoader:
    """
    Centralized data loader for all epidemiological and demographic data.
    
    This class provides methods to load and validate all data types used
    in the transmission modeling analysis.
    """
```

#### Methods

##### `load_epidemiological_data(state: Optional[str] = None) -> pd.DataFrame`
Load epidemiological time series data.

**Parameters:**
- `state`: Optional state name. If None, loads all states.

**Returns:**
- `pd.DataFrame`: Epidemiological time series

##### `load_demographic_data(state: Optional[str] = None) -> pd.DataFrame`
Load demographic data (births, population).

**Parameters:**
- `state`: Optional state name. If None, loads all states.

**Returns:**
- `pd.DataFrame`: Demographic data

##### `load_vaccination_data(state: Optional[str] = None) -> pd.DataFrame`
Load vaccination coverage and campaign data.

**Parameters:**
- `state`: Optional state name. If None, loads all states.

**Returns:**
- `pd.DataFrame`: Vaccination data

##### `load_all_state_data(state: str) -> Dict[str, pd.DataFrame]`
Load all data for a specific state.

**Parameters:**
- `state`: State name

**Returns:**
- `Dict[str, pd.DataFrame]`: Dictionary containing all data types for the state

### EpidemiologicalDataProcessor

Processor for epidemiological surveillance data.

```python
class EpidemiologicalDataProcessor:
    """
    Processor for epidemiological surveillance data.
    
    This class handles the processing of case-based surveillance data,
    including classification of untested cases and time series aggregation.
    """
```

#### Methods

##### `process_surveillance_data(raw_data: pd.DataFrame, state: str) -> pd.DataFrame`
Process raw surveillance data for a state.

**Parameters:**
- `raw_data`: Raw surveillance data
- `state`: State name

**Returns:**
- `pd.DataFrame`: Processed epidemiological time series

##### `compute_age_distribution(data: pd.DataFrame) -> pd.DataFrame`
Compute age distribution of cases.

**Parameters:**
- `data`: Processed case data

**Returns:**
- `pd.DataFrame`: Age distribution

##### `estimate_reporting_rate(time_series: pd.DataFrame, expected_annual_burden: float) -> Dict[str, float]`
Estimate surveillance reporting rate.

**Parameters:**
- `time_series`: Processed time series data
- `expected_annual_burden`: Expected annual case burden

**Returns:**
- `Dict[str, float]`: Reporting rate estimates

## Utilities Module (`src.utils`)

### Mathematical Utilities

#### `low_mid_high(samples: np.ndarray, confidence_levels: list = [0.5, 0.95]) -> Tuple[np.ndarray, ...]`
Compute confidence intervals from sample arrays.

**Parameters:**
- `samples`: Array of samples with shape (n_samples, n_timepoints)
- `confidence_levels`: List of confidence levels (e.g., [0.5, 0.95])

**Returns:**
- `Tuple[np.ndarray, ...]`: Confidence interval bounds

#### `fit_quality_metrics(observed: np.ndarray, predicted: np.ndarray, prediction_intervals: Optional[Tuple[np.ndarray, np.ndarray]] = None, verbose: bool = True) -> dict`
Compute model fit quality metrics.

**Parameters:**
- `observed`: Observed data values
- `predicted`: Predicted values (median/mean)
- `prediction_intervals`: Optional tuple of (lower, upper) prediction bounds
- `verbose`: Whether to print results

**Returns:**
- `dict`: Dictionary of fit quality metrics

### File Utilities

#### `save_pickle(obj: Any, filepath: Union[str, Path]) -> None`
Save an object to a pickle file.

#### `load_pickle(filepath: Union[str, Path]) -> Any`
Load an object from a pickle file.

#### `save_dataframe(df: pd.DataFrame, filepath: Union[str, Path], format: str = 'csv', **kwargs) -> None`
Save a pandas DataFrame to file.

#### `load_dataframe(filepath: Union[str, Path], format: Optional[str] = None, **kwargs) -> pd.DataFrame`
Load a pandas DataFrame from file.

### Logging Utilities

#### `setup_logging(level: str = "INFO", log_file: Optional[Path] = None, format_string: Optional[str] = None, use_rich: bool = True) -> None`
Setup logging configuration for the package.

#### `get_logger(name: str) -> logging.Logger`
Get a logger instance for the given name.

## Constants and Configuration

### States and Regions

```python
STATES_SOUTHERN_NIGERIA = [
    "abia", "akwa_ibom", "anambra", "bayelsa", "cross_river", "delta",
    "ebonyi", "edo", "ekiti", "enugu", "imo", "lagos", "ogun", "ondo", 
    "osun", "oyo", "rivers"
]
```

### Vaccination Parameters

```python
VACCINE_EFFICACY = {
    "mcv1": 0.825,  # First dose measles vaccine
    "mcv2": 0.95,   # Second dose measles vaccine  
    "sia": 0.9      # Supplementary immunization activity
}
```

### Model Parameters

```python
MODEL_PARAMETERS = {
    "beta_correlation": 3.0,
    "tau": 26,
    "mu_initial_guess": 0.1,
    "convergence_tolerance": 1e-6,
    "max_optimization_iterations": 1000
}
```

## Data Structures

### ModelResults

```python
@dataclass
class ModelResults:
    """Container for model fitting results."""
    
    parameters: Dict[str, Any]
    log_likelihood: float
    convergence_info: Dict[str, Any]
    compartment_data: pd.DataFrame
    posterior_samples: Optional[np.ndarray] = None
```

### CohortAnalysisResult

```python
@dataclass
class CohortAnalysisResult:
    """Results of survival analysis for a birth cohort."""
    
    cohort_year: int
    cohort_size: int
    immunity_fractions: Dict[str, float]  # Fraction immune by source
    infection_fraction: float  # Fraction destined for infection
    age_infection_dist: np.ndarray  # Age distribution of infections
```

### VaccinationOpportunity

```python
@dataclass
class VaccinationOpportunity:
    """Represents a vaccination opportunity with timing and coverage."""
    
    name: str
    time_months: float  # Age in months when opportunity occurs
    efficacy: float  # Probability of seroconversion
    coverage_func: callable  # Function to compute coverage for birth cohort
```

## Error Handling

The package defines custom exceptions for different error conditions:

- `ValueError`: Invalid input parameters or data
- `FileNotFoundError`: Missing required data files
- `RuntimeError`: Model not fitted or other runtime errors

All functions include comprehensive input validation and provide informative error messages.

## Type Hints

The package uses comprehensive type hints throughout for better IDE support and code clarity. All public functions include proper type annotations for parameters and return values.

## Logging

The package uses structured logging with different levels:
- `DEBUG`: Detailed diagnostic information
- `INFO`: General information about program execution
- `WARNING`: Warning messages for potential issues
- `ERROR`: Error messages for serious problems

Use `get_logger(__name__)` to get a logger instance in any module.
