# Research Methodology - Intensification Package

This document describes the scientific methodology implemented in the modernized intensification package, based on the research paper "Routine immunization intensification, vaccination campaigns, and measles transmission in Southern Nigeria" (2025).

## Overview

The package implements a comprehensive framework for analyzing measles transmission dynamics and vaccination campaign effectiveness using state-level stochastic transmission models. The methodology combines epidemiological surveillance data, demographic surveys, and vaccination implementation data to estimate intervention impacts while accounting for under-reporting and demographic changes.

## Core Methodology

### 1. Neighborhood SIR Transmission Model

The package implements a stochastic Susceptible-Infectious-Recovered (SIR) transmission model with several key features:

#### Model Structure
- **Compartments**: Population divided into Susceptible (S), Infectious (I), and Recovered (R) states
- **Time Scale**: Semi-monthly time steps (26 periods per year) to capture seasonal dynamics
- **Spatial Correlation**: Neighborhood effects for seasonality correlation across geopolitical zones
- **Stochastic Process**: Log-normal distribution for transmission rates with seasonal variation

#### Mathematical Formulation

The model tracks population dynamics through:

```
S(t+1) = S(t) + B(t) - E(t) - V(t)
I(t+1) = E(t)  
R(t+1) = R(t) + I(t)
```

Where:
- `S(t)`: Susceptible population at time t
- `I(t)`: Infectious population at time t  
- `E(t)`: New infections (exposed) at time t
- `B(t)`: Births entering susceptible population
- `V(t)`: Vaccinations removing susceptibles

#### Transmission Process

New infections follow:
```
E(t) ~ Poisson(S(t) * I(t) * β(t))
```

Where `β(t)` is the time-varying transmission rate with seasonal structure:
```
log(β(t)) = X(t) * γ + ε(t)
```

- `X(t)`: Design matrix for seasonal effects
- `γ`: Seasonal coefficients with smoothness prior
- `ε(t)`: Random variation

#### Seasonality Prior

The model uses a second-order random walk prior for seasonal smoothness:
```
P(γ) ∝ exp(-0.5 * γ^T * K * γ)
```

Where `K` is the penalty matrix enforcing periodic smoothness across the 26 semi-monthly periods.

### 2. Vaccination Campaign Effects

#### SIA (Supplementary Immunization Activity) Modeling

Vaccination campaigns are modeled as discrete events that remove susceptible individuals:

```
V_sia(t) = μ_i * D_i(t)
```

Where:
- `μ_i`: Efficacy of campaign i (fraction of doses reaching susceptibles)
- `D_i(t)`: Doses delivered in campaign i at time t

#### Routine Vaccination

Routine vaccination (MCV1, MCV2) is modeled as continuous removal from birth cohorts:
```
V_routine(t) = B(t) * c(t) * ε
```

Where:
- `c(t)`: Time-varying coverage rate
- `ε`: Vaccine efficacy

### 3. Survival Analysis for Immunity Estimation

The package implements survival analysis to estimate the fraction of birth cohorts remaining susceptible across vaccination opportunities.

#### Cohort Partitioning

Each birth cohort is partitioned by immunity source:
```
P(immunity_source | birth_year) = {I, V1, V2, V_c1, ..., V_cM}
```

Where:
- `I`: Infection-derived immunity
- `V1, V2`: Routine vaccination immunity (MCV1, MCV2)
- `V_c1, ..., V_cM`: Campaign vaccination immunity

#### Recursive Calculation

For each vaccination opportunity at time `t_s` with efficacy `ε(s)` and coverage `C(s|b)`:

```
P(s=s'|b) = ε(s') * C(s'|b) * [1 - P(s=I|b) * P(a ≤ t_s' - b | s=I, b) - Σ P(s=s''|b)]
```

This accounts for:
- Vaccination efficacy and coverage
- Survival probability (not infected before vaccination)
- Previous vaccination opportunities

#### Normalization Condition

The infection fraction is solved to satisfy:
```
Σ P(s|b) = 1
```

Using bisection method to find the infection fraction that balances all immunity sources.

### 4. Case Classification and Under-reporting

#### Logistic Regression for Untested Cases

The package implements logistic regression to classify untested clinically compatible cases:

```
P(confirmed | age, doses, time) = σ(β₀ + β₁I_{d=1} + β₂I_{d≥2} + β₃I_{d=∅} + β₄I_{a>5} + ε_t)
```

Where:
- `σ`: Logistic function
- `I_c`: Indicator functions for conditions
- `ε_t`: Time-varying random effect

#### Features Used
- **Age**: Binary indicator for age > 5 years
- **Vaccination Status**: Indicators for 1 dose, 2+ doses, missing dose history
- **Temporal Effects**: Smooth variation in confirmation rates over time

#### Temporal Correlation

Confirmation probabilities include temporal correlation through:
- 2-month time periods for grouping
- Smooth interpolation of confirmation rates
- Correlation with laboratory testing activity

### 5. Demographic Data Processing

#### Birth Rate Estimation

The package uses regression with post-stratification to estimate birth rates:

1. **Yearly Births**: Regression on survey data with state-level post-stratification
2. **Seasonal Allocation**: Empirical seasonality profiles from birth date distributions
3. **Monthly Births**: Combination of yearly estimates with seasonal patterns

#### Coverage Estimation

MCV1 coverage estimation follows similar approach:
1. **Survey Data**: DHS and MICS household surveys
2. **Age Targeting**: 12-23 month age group for MCV1 coverage
3. **Temporal Interpolation**: Smooth interpolation between survey years
4. **Validation**: Comparison with official survey estimates

### 6. Model Fitting and Optimization

#### Likelihood Function

The full likelihood combines multiple components:
```
L(θ) = L_transmission(θ) * L_S0_prior(θ) * L_seasonality(θ)
```

Where:
- `L_transmission`: Poisson likelihood for case counts
- `L_S0_prior`: Prior on initial susceptible population
- `L_seasonality`: Smoothness prior on seasonal effects

#### Optimization

Model fitting uses L-BFGS-B optimization with:
- **Parameter Bounds**: Constrain SIA efficacies to [0,1], log(S0) to reasonable range
- **Initial Values**: Informed starting points based on prior knowledge
- **Convergence Criteria**: Tolerance on likelihood improvement

#### Uncertainty Quantification

The package provides uncertainty estimates through:
- **Profile Likelihoods**: For individual parameters
- **Bootstrap Sampling**: For derived quantities
- **Prediction Intervals**: For forecasts and model outputs

### 7. Model Validation

#### Out-of-Sample Testing

The package implements forecast validation:
1. **Training Period**: Fit model to data through end of 2020
2. **Forecast Period**: Predict 2021-2024 with known campaign timing
3. **Validation Metrics**: Coverage of prediction intervals, forecast accuracy

#### Cross-Validation

Additional validation includes:
- **Serological Validation**: Comparison with independent serological surveys
- **Age Distribution Validation**: Consistency with observed age patterns
- **Temporal Validation**: Ability to capture outbreak timing and magnitude

### 8. Comparative Analysis

#### Campaign Effectiveness Metrics

The package computes standardized metrics for comparing interventions:

1. **Per-Dose Efficacy**: Fraction of delivered doses that immunize susceptibles
2. **Population Impact**: Reduction in susceptible population
3. **Transmission Impact**: Effect on force of infection
4. **Cost-Effectiveness**: Cases prevented per dose delivered

#### Statistical Comparison

Comparisons account for:
- **Uncertainty Propagation**: Full posterior distributions for efficacy estimates
- **Temporal Context**: Timing relative to transmission seasons
- **Demographic Context**: Target population susceptibility levels

## Implementation Details

### Numerical Considerations

#### Stability
- **Log-Space Calculations**: S0 prior in log-space for numerical stability
- **Regularization**: Smoothness priors prevent overfitting
- **Bounds Checking**: Parameter constraints ensure biological plausibility

#### Computational Efficiency
- **Matrix Operations**: Efficient sparse matrix operations for seasonality
- **Vectorization**: NumPy vectorized operations where possible
- **Caching**: Intermediate results cached to avoid recomputation

### Data Requirements

#### Minimum Data Requirements
- **Case Data**: Daily or weekly case counts with age information
- **Demographic Data**: Birth rates and population estimates
- **Vaccination Data**: Campaign timing and coverage estimates
- **Survey Data**: Routine vaccination coverage from household surveys

#### Data Quality Considerations
- **Missing Data**: Robust handling of missing values
- **Outliers**: Detection and handling of anomalous data points
- **Validation**: Cross-checks between different data sources

### Software Architecture

#### Modular Design
- **Separation of Concerns**: Clear separation between data, models, and analysis
- **Extensibility**: Easy to add new model components or data sources
- **Testability**: Comprehensive unit and integration tests

#### Configuration Management
- **Parameter Files**: YAML/JSON configuration for reproducibility
- **Environment Variables**: Flexible deployment across environments
- **Validation**: Pydantic validation for configuration parameters

## Limitations and Assumptions

### Model Assumptions
1. **Homogeneous Mixing**: Population mixing assumed homogeneous within states
2. **Lifelong Immunity**: Infection and vaccination provide lifelong immunity
3. **No Waning**: Vaccine-derived immunity does not wane over time
4. **Reporting Consistency**: Surveillance system characteristics stable over time

### Data Limitations
1. **Under-reporting**: Surveillance captures only fraction of true cases
2. **Age Misclassification**: Potential errors in reported ages
3. **Vaccination History**: Self-reported vaccination status may be inaccurate
4. **Spatial Aggregation**: State-level analysis may miss sub-state heterogeneity

### Methodological Limitations
1. **Model Structure**: SIR framework may not capture all transmission dynamics
2. **Seasonality**: Fixed seasonal pattern may not account for climate variation
3. **Campaign Modeling**: Simplified representation of complex implementation
4. **Uncertainty**: Some sources of uncertainty not fully captured

## Future Extensions

### Potential Enhancements
1. **Age Structure**: Explicit age-structured transmission model
2. **Spatial Structure**: Sub-state spatial transmission dynamics
3. **Vaccine Waning**: Incorporation of waning vaccine immunity
4. **Multiple Pathogens**: Extension to other vaccine-preventable diseases

### Methodological Improvements
1. **Machine Learning**: Integration of ML methods for pattern recognition
2. **Bayesian Methods**: Full Bayesian inference with MCMC
3. **Real-time Analysis**: Streaming data processing for real-time monitoring
4. **Causal Inference**: Formal causal inference methods for intervention effects

This methodology provides a robust framework for analyzing vaccination campaign effectiveness while accounting for the complex dynamics of measles transmission in high-burden settings.
