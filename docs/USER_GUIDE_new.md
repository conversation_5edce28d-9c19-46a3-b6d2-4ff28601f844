# User Guide for Non-Programmers - Intensification Package

## Overview

This guide is specifically written for researchers **without coding experience** who want to use the intensification package to analyze measles transmission and vaccination campaigns.

## Prerequisites

### What You Need
- Your epidemiological data (case reports, vaccination records)
- Basic computer skills (opening files, using command line)
- About 2-3 hours for initial setup and learning

### What You Don't Need
- Programming experience
- Deep understanding of the mathematical models
- Advanced computer science knowledge

## Step-by-Step Setup

### 1. Installation (One-Time Setup)

#### Option A: Simple Installation (Recommended)
```bash
# Open Terminal (Mac/Linux) or Command Prompt (Windows)
# Copy and paste this command:
pip install git+https://github.com/NThakkar-IDM/intensification.git
```

#### Option B: If You Have Conda
```bash
# If you use Anaconda/Miniconda:
conda create -n measles-analysis python=3.12
conda activate measles-analysis
pip install git+https://github.com/NThakkar-IDM/intensification.git
```

### 2. Verify Installation
```bash
# Test that it works:
intensification version
# Should show: "Intensification Package v2.0.0"
```

## Preparing Your Data

### Required Data Files

You need to prepare 4 main data files. **Don't worry** - we provide templates!

#### 1. **Case Data** (`my_cases.csv`)
Your measles case reports with these columns:
- `date`: When case was reported (YYYY-MM-DD format)
- `state`: Which state/region
- `age`: Age of patient in years
- `case_status`: confirmed, rejected, or untested
- `vaccine_doses`: Number of measles vaccines received (0, 1, 2, or blank)

**Example:**
```csv
date,state,age,case_status,vaccine_doses
2020-01-15,lagos,2.5,confirmed,1
2020-01-16,lagos,1.2,untested,0
2020-01-17,oyo,3.1,rejected,2
```

#### 2. **Population Data** (`my_population.csv`)
Basic population information:
- `state`: State/region name
- `population`: Total population
- `birth_rate`: Births per 1000 people per year

**Example:**
```csv
state,population,birth_rate
lagos,15000000,28.5
oyo,8000000,30.2
```

#### 3. **Vaccination Coverage** (`my_coverage.csv`)
Routine vaccination coverage by year:
- `state`: State/region name
- `year`: Year
- `mcv1_coverage`: Fraction of children getting first measles vaccine (0.0 to 1.0)

**Example:**
```csv
state,year,mcv1_coverage
lagos,2020,0.75
lagos,2021,0.78
oyo,2020,0.68
```

#### 4. **Vaccination Campaigns** (`my_campaigns.csv`)
Information about mass vaccination campaigns:
- `state`: State/region name
- `campaign_date`: When campaign happened (YYYY-MM-DD)
- `target_age_min`: Minimum age targeted (in months)
- `target_age_max`: Maximum age targeted (in months)
- `doses_delivered`: Number of vaccine doses given

**Example:**
```csv
state,campaign_date,target_age_min,target_age_max,doses_delivered
lagos,2018-03-15,9,60,500000
lagos,2022-01-10,9,60,750000
```

### Data Preparation Tips

1. **Use Excel or Google Sheets** to prepare your data
2. **Save as CSV files** (not Excel format)
3. **Check date formats** - use YYYY-MM-DD (e.g., 2020-01-15)
4. **Use consistent state names** - all lowercase, replace spaces with underscores
5. **Missing data** - leave cells blank, don't use "N/A" or "NULL"

## Running Your Analysis

### Step 1: Check Your Data
```bash
# Put your CSV files in a folder called "_data"
# Then check if the package can read them:
intensification data-check --states lagos oyo
```

**What you'll see:**
- ✅ Green checkmarks = data is ready
- ❌ Red X marks = data has problems (check the error messages)

### Step 2: Run Basic Analysis
```bash
# Analyze one state:
intensification analyze lagos --output my_results/

# This will:
# - Fit the transmission model to your data
# - Estimate vaccination campaign effectiveness
# - Generate summary statistics
# - Save results to "my_results/" folder
```

### Step 3: Generate Plots
```bash
# Create all standard plots:
intensification plot lagos --figures all --output my_plots/

# Or create specific plots:
intensification plot lagos --figures transmission --output my_plots/
intensification plot lagos --figures campaigns --output my_plots/
```

### Step 4: Validate Your Model
```bash
# Test how well the model predicts future cases:
intensification validate --states lagos --forecast-years 2
```

## Understanding Your Results

### Output Files You'll Get

#### 1. **Summary Report** (`my_results/lagos_summary.txt`)
Plain English summary including:
- How well the model fits your data
- Estimated effectiveness of each vaccination campaign
- Key statistics and confidence intervals

#### 2. **Detailed Results** (`my_results/lagos_detailed.csv`)
Spreadsheet with:
- Model parameters and their uncertainty
- Campaign effectiveness estimates
- Model validation metrics

#### 3. **Plots** (`my_plots/`)
- `transmission_dynamics.png`: Cases over time with model fit
- `campaign_effectiveness.png`: How effective each campaign was
- `model_validation.png`: How well model predicts held-out data

### Interpreting Key Results

#### **Campaign Effectiveness**
- **Values near 0.1 (10%)**: Campaign reached about 10% of susceptible children
- **Values near 0.3 (30%)**: Very effective campaign
- **Higher values = better campaigns**

#### **Model Fit Quality**
- **R² > 0.7**: Good model fit
- **R² > 0.5**: Acceptable fit
- **R² < 0.3**: Poor fit (check your data)

#### **Confidence Intervals**
- **Narrow intervals**: Precise estimates
- **Wide intervals**: Uncertain estimates (need more data)

## Troubleshooting Common Issues

### "File not found" errors
- Check that your CSV files are in the `_data/` folder
- Verify file names match exactly (case-sensitive)
- Make sure files are saved as CSV, not Excel format

### "Invalid state name" errors
- Use lowercase state names
- Replace spaces with underscores (e.g., "cross_river" not "Cross River")
- Check spelling against the supported states list

### "Missing required columns" errors
- Check that your CSV files have the exact column names shown in examples
- Remove any extra spaces in column headers
- Make sure you didn't accidentally delete required columns

### Poor model fit (low R²)
- Check if your case data has obvious errors (negative ages, future dates)
- Verify that vaccination campaign dates are correct
- Consider if your region has different transmission patterns

## Getting Help

### 1. Check the Documentation
- Read `README_new.md` for technical details
- Look at `examples/basic_analysis_new.py` for code examples
- Review `docs/METHODOLOGY_new.md` for scientific background

### 2. Common Questions

**Q: Can I analyze multiple states at once?**
A: Yes! Use: `intensification analyze state1,state2,state3`

**Q: What if I don't have vaccination campaign data?**
A: The package can still analyze routine vaccination and natural transmission patterns.

**Q: How do I know if my results are reliable?**
A: Check the R² score (>0.5 is good) and look at the confidence intervals in your results.

**Q: Can I use this for other diseases?**
A: The package is specifically designed for measles, but the methods could be adapted.

### 3. Technical Support
- Open an issue on GitHub with your error messages
- Include your data format (first few rows of each CSV file)
- Describe what you were trying to do when the error occurred

## Advanced Usage (Optional)

### Custom Configuration
Create a `config.yaml` file to customize analysis:
```yaml
analysis:
  default_state: "my_state"
  analysis_start_year: 2015
  analysis_end_year: 2023
  confidence_levels: [0.5, 0.95]

model:
  mcv1_efficacy: 0.825  # Adjust if you have different vaccine efficacy
  sia_efficacy: 0.9     # Campaign vaccine efficacy
```

### Batch Processing
Analyze multiple states automatically:
```bash
# Create a list of states in states.txt, then:
intensification batch-analyze --states-file states.txt --output batch_results/
```

## Best Practices

1. **Start Small**: Begin with one state and one year of data
2. **Validate Data**: Always run `data-check` before analysis
3. **Check Results**: Look at plots to see if results make sense
4. **Document Everything**: Keep notes about data sources and any modifications
5. **Save Originals**: Keep backup copies of your original data files

## Conclusion

This package makes sophisticated measles transmission modeling accessible to public health researchers without requiring programming skills. The key is careful data preparation and understanding what the results mean for your public health questions.

Remember: **The models are only as good as your data**. Spend time ensuring your data is accurate and complete before running analyses.
