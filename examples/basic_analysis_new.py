#!/usr/bin/env python3
"""
Basic Analysis Example - Modernized Intensification Package

This script demonstrates basic usage of the modernized intensification package
for analyzing measles transmission dynamics and vaccination campaign effectiveness.

This example shows how to:
1. Load and validate data for a specific state
2. Fit a neighborhood SIR transmission model
3. Analyze vaccination campaign effectiveness
4. Generate basic visualizations
5. Compute model validation metrics

Author: Research Team
Date: 2025
"""

import sys
from pathlib import Path
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core import NeighborhoodSIRModel, SurvivalAnalyzer, Config
from src.data import DataLoader, EpidemiologicalDataProcessor
from src.utils import setup_logging, get_logger, low_mid_high
from src.utils.constants import validate_state, STATES_SOUTHERN_NIGERIA


def main():
    """Main analysis function."""
    # Setup logging
    setup_logging(level="INFO", use_rich=True)
    logger = get_logger(__name__)
    
    logger.info("Starting basic analysis example")
    
    # Configuration
    config = Config()
    state = "lagos"  # Default to Lagos state
    
    try:
        # Step 1: Load and validate data
        logger.info(f"Loading data for {state.upper()} state")
        data_loader = DataLoader()
        
        # Check data availability
        availability = data_loader.validate_data_availability([state])
        if not availability[state]:
            logger.error(f"Data not available for {state}")
            return
        
        # Load all data for the state
        state_data = data_loader.load_all_state_data(state)
        logger.info("Data loaded successfully")
        
        # Step 2: Process epidemiological data
        logger.info("Processing epidemiological data")
        epi_processor = EpidemiologicalDataProcessor()
        
        # For this example, we'll create synthetic surveillance data
        # In practice, this would come from the actual surveillance system
        epi_data = create_synthetic_epi_data(state)
        processed_epi = epi_processor.process_surveillance_data(epi_data, state)
        
        logger.info(f"Processed {len(processed_epi)} time points")
        
        # Step 3: Prepare model inputs
        logger.info("Preparing model inputs")
        model_data = prepare_model_data(processed_epi, state_data)
        
        # Step 4: Fit transmission model
        logger.info("Fitting neighborhood SIR model")
        model = NeighborhoodSIRModel()
        
        # Fit the model
        results = model.fit(
            data=model_data['time_series'],
            sia_effects=model_data['sia_effects'],
            s0_prior=model_data['s0_prior'],
            s0_var=model_data['s0_var']
        )
        
        logger.info("Model fitting completed")
        logger.info(f"Log-likelihood: {results.log_likelihood:.2f}")
        logger.info(f"Convergence: {results.convergence_info['success']}")
        
        # Step 5: Analyze vaccination campaigns
        logger.info("Analyzing vaccination campaigns")
        campaign_analysis = analyze_campaigns(results, model_data)
        
        # Step 6: Generate visualizations
        logger.info("Generating visualizations")
        create_basic_plots(results, model_data, campaign_analysis, state)
        
        # Step 7: Model validation
        logger.info("Computing validation metrics")
        validation_metrics = compute_validation_metrics(results, model_data)
        
        # Step 8: Summary report
        generate_summary_report(results, campaign_analysis, validation_metrics, state)
        
        logger.info("Analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise


def create_synthetic_epi_data(state: str) -> pd.DataFrame:
    """
    Create synthetic epidemiological data for demonstration.
    
    In practice, this would be replaced with actual surveillance data loading.
    """
    logger = get_logger(__name__)
    logger.info("Creating synthetic epidemiological data")
    
    # Create date range
    dates = pd.date_range(start='2009-01-01', end='2023-12-31', freq='D')
    
    # Simulate case data with seasonal pattern
    np.random.seed(42)  # For reproducibility
    
    # Base transmission rate with seasonality
    day_of_year = dates.dayofyear
    seasonal_effect = 1 + 0.3 * np.sin(2 * np.pi * day_of_year / 365.25)
    
    # Simulate outbreaks and control periods
    base_rate = np.ones(len(dates)) * 0.1
    
    # Add outbreak periods
    outbreak_periods = [
        ('2013-01-01', '2013-06-30', 2.0),
        ('2022-01-01', '2022-08-31', 1.5)
    ]
    
    for start, end, multiplier in outbreak_periods:
        mask = (dates >= start) & (dates <= end)
        base_rate[mask] *= multiplier
    
    # Generate case counts
    expected_cases = base_rate * seasonal_effect
    cases = np.random.poisson(expected_cases)
    
    # Create synthetic individual case records
    case_records = []
    for i, (date, case_count) in enumerate(zip(dates, cases)):
        for _ in range(int(case_count)):
            # Simulate case characteristics
            age = np.random.exponential(2.5)  # Exponential age distribution
            vaccine_doses = np.random.choice([0, 1, 2, np.nan], p=[0.3, 0.4, 0.2, 0.1])
            
            # Case status based on testing availability
            if np.random.random() < 0.7:  # 70% tested
                case_status = np.random.choice(['confirmed', 'rejected'], p=[0.6, 0.4])
                lab_result = case_status
            else:
                case_status = 'untested'
                lab_result = np.nan
            
            case_records.append({
                'state': state,
                'date': date,
                'age': age,
                'case_status': case_status,
                'vaccine_doses': vaccine_doses,
                'lab_result': lab_result,
                'symptoms': 'fever_rash'  # Simplified
            })
    
    return pd.DataFrame(case_records)


def prepare_model_data(processed_epi: pd.DataFrame, state_data: dict) -> dict:
    """Prepare data for model fitting."""
    logger = get_logger(__name__)
    logger.info("Preparing model data")
    
    # Aggregate to semi-monthly periods
    processed_epi['period'] = processed_epi['date'].dt.to_period('SM')
    time_series = processed_epi.groupby('period').agg({
        'cases': 'sum',
        'total_reports': 'sum'
    }).reset_index()
    
    # Add synthetic birth and vaccination data
    n_periods = len(time_series)
    
    # Synthetic births (roughly constant with seasonal variation)
    base_births = 1000  # births per semi-month
    seasonal_births = base_births * (1 + 0.1 * np.sin(2 * np.pi * np.arange(n_periods) / 26))
    time_series['adj_births'] = seasonal_births
    
    # Adjusted cases (same as cases for this example)
    time_series['adj_cases_p'] = time_series['cases']
    
    # Synthetic SIA effects (vaccination campaigns)
    sia_effects = np.zeros((n_periods, 6))  # 6 campaigns
    
    # Add campaigns at specific periods
    campaign_periods = [50, 100, 120, 180, 200, 260]  # Example periods
    for i, period in enumerate(campaign_periods):
        if period < n_periods:
            sia_effects[period, i] = 50000  # doses delivered
    
    # Prior for initial susceptible population
    s0_prior = 100000  # 100k susceptible individuals
    s0_var = 10000     # variance
    
    return {
        'time_series': time_series,
        'sia_effects': sia_effects,
        's0_prior': s0_prior,
        's0_var': s0_var
    }


def analyze_campaigns(results, model_data):
    """Analyze vaccination campaign effectiveness."""
    logger = get_logger(__name__)
    logger.info("Analyzing campaign effectiveness")
    
    # Extract SIA efficacy parameters
    sia_efficacies = results.parameters['mu']
    
    # Calculate doses delivered per campaign
    total_doses = model_data['sia_effects'].sum(axis=0)
    
    # Campaign analysis
    campaigns = []
    for i, (efficacy, doses) in enumerate(zip(sia_efficacies, total_doses)):
        if doses > 0:  # Only analyze campaigns with doses
            effective_doses = efficacy * doses
            campaigns.append({
                'campaign_id': i + 1,
                'total_doses': doses,
                'efficacy': efficacy,
                'effective_doses': effective_doses,
                'effectiveness_per_dose': efficacy
            })
    
    return pd.DataFrame(campaigns)


def create_basic_plots(results, model_data, campaign_analysis, state):
    """Create basic visualization plots."""
    logger = get_logger(__name__)
    logger.info("Creating visualization plots")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'Measles Transmission Analysis - {state.upper()} State', fontsize=16)
    
    # Plot 1: Time series of cases
    ax1 = axes[0, 0]
    time_series = model_data['time_series']
    ax1.plot(time_series.index, time_series['cases'], 'o-', label='Observed Cases')
    ax1.set_title('Epidemiological Time Series')
    ax1.set_xlabel('Time Period')
    ax1.set_ylabel('Cases')
    ax1.legend()
    
    # Plot 2: Compartment dynamics
    ax2 = axes[0, 1]
    compartments = results.compartment_data
    ax2.plot(compartments['time'], compartments['S_t'], label='Susceptible')
    ax2.plot(compartments['time'], compartments['I_t'], label='Infectious')
    ax2.set_title('Model Compartments')
    ax2.set_xlabel('Time')
    ax2.set_ylabel('Population')
    ax2.legend()
    
    # Plot 3: Campaign effectiveness
    ax3 = axes[1, 0]
    if not campaign_analysis.empty:
        ax3.bar(campaign_analysis['campaign_id'], campaign_analysis['efficacy'])
        ax3.set_title('Campaign Efficacy')
        ax3.set_xlabel('Campaign ID')
        ax3.set_ylabel('Efficacy')
    
    # Plot 4: Model fit quality
    ax4 = axes[1, 1]
    # Simple residual plot
    observed = time_series['cases'].values
    predicted = compartments['I_t'].values[:len(observed)]
    residuals = observed - predicted
    ax4.scatter(predicted, residuals)
    ax4.axhline(y=0, color='r', linestyle='--')
    ax4.set_title('Model Residuals')
    ax4.set_xlabel('Predicted')
    ax4.set_ylabel('Residuals')
    
    plt.tight_layout()
    
    # Save plot
    output_path = Path('_plots') / f'{state}_basic_analysis_new.png'
    output_path.parent.mkdir(exist_ok=True)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logger.info(f"Plot saved to {output_path}")
    
    plt.show()


def compute_validation_metrics(results, model_data):
    """Compute model validation metrics."""
    logger = get_logger(__name__)
    logger.info("Computing validation metrics")
    
    # Extract observed and predicted values
    time_series = model_data['time_series']
    compartments = results.compartment_data
    
    observed = time_series['cases'].values
    predicted = compartments['I_t'].values[:len(observed)]
    
    # Compute metrics
    from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
    
    metrics = {
        'r2_score': r2_score(observed, predicted),
        'mae': mean_absolute_error(observed, predicted),
        'rmse': np.sqrt(mean_squared_error(observed, predicted)),
        'mape': np.mean(np.abs((observed - predicted) / (observed + 1e-10))) * 100
    }
    
    return metrics


def generate_summary_report(results, campaign_analysis, validation_metrics, state):
    """Generate a summary report of the analysis."""
    logger = get_logger(__name__)
    logger.info("Generating summary report")
    
    report = f"""
    
    MEASLES TRANSMISSION ANALYSIS SUMMARY
    =====================================
    
    State: {state.upper()}
    Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
    
    MODEL FITTING RESULTS
    ---------------------
    Log-likelihood: {results.log_likelihood:.2f}
    Convergence: {results.convergence_info['success']}
    Iterations: {results.convergence_info.get('nit', 'N/A')}
    
    VACCINATION CAMPAIGN ANALYSIS
    ----------------------------
    Number of campaigns analyzed: {len(campaign_analysis)}
    """
    
    if not campaign_analysis.empty:
        report += f"""
    Average campaign efficacy: {campaign_analysis['efficacy'].mean():.3f}
    Range of efficacy: {campaign_analysis['efficacy'].min():.3f} - {campaign_analysis['efficacy'].max():.3f}
    Total effective doses: {campaign_analysis['effective_doses'].sum():.0f}
    """
    
    report += f"""
    
    MODEL VALIDATION METRICS
    ------------------------
    R² Score: {validation_metrics['r2_score']:.3f}
    Mean Absolute Error: {validation_metrics['mae']:.2f}
    Root Mean Square Error: {validation_metrics['rmse']:.2f}
    Mean Absolute Percentage Error: {validation_metrics['mape']:.1f}%
    
    INTERPRETATION
    --------------
    This analysis demonstrates the basic functionality of the modernized
    intensification package for measles transmission modeling. The model
    successfully fits the epidemiological data and provides estimates of
    vaccination campaign effectiveness.
    
    Note: This example uses synthetic data for demonstration purposes.
    Real analysis would use actual surveillance and survey data.
    """
    
    print(report)
    
    # Save report to file
    output_path = Path('_plots') / f'{state}_analysis_report_new.txt'
    output_path.parent.mkdir(exist_ok=True)
    with open(output_path, 'w') as f:
        f.write(report)
    
    logger.info(f"Report saved to {output_path}")


if __name__ == "__main__":
    main()
