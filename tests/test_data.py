"""
Unit tests for the data module of the intensification package.

This module tests data loading, processing, and validation functionality.
"""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from src.data import (
    DataLoader,
    EpidemiologicalDataProcessor
)


class TestDataLoader:
    """Test DataLoader class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.data_loader = DataLoader()
        
        # Create mock data
        self.mock_states_data = pd.DataFrame({
            'state': ['lagos', 'oyo', 'rivers'],
            'region': ['south_west', 'south_west', 'south_south']
        })
        
        self.mock_epi_data = pd.DataFrame({
            'state': ['lagos'] * 100,
            'date': pd.date_range('2020-01-01', periods=100),
            'cases': np.random.poisson(5, 100)
        })
        
        self.mock_demo_data = pd.DataFrame({
            'state': ['lagos'],
            'monthly_births': [1000],
            'population': [15000000]
        })
    
    def test_initialization(self):
        """Test data loader initialization."""
        assert isinstance(self.data_loader.data_dir, Path)
        assert hasattr(self.data_loader, 'config')
        assert hasattr(self.data_loader, 'logger')
    
    @patch('src.data.loaders.load_dataframe')
    def test_load_states_and_regions(self, mock_load):
        """Test loading states and regions data."""
        mock_load.return_value = self.mock_states_data
        
        result = self.data_loader.load_states_and_regions()
        
        assert isinstance(result, pd.DataFrame)
        assert 'state' in result.columns
        assert 'region' in result.columns
        mock_load.assert_called_once()
    
    @patch('src.data.loaders.load_dataframe')
    def test_load_states_missing_columns(self, mock_load):
        """Test loading states data with missing columns."""
        mock_load.return_value = pd.DataFrame({'wrong_column': [1, 2, 3]})
        
        with pytest.raises(ValueError, match="Missing required columns"):
            self.data_loader.load_states_and_regions()
    
    @patch('src.data.loaders.load_dataframe')
    def test_load_epidemiological_data(self, mock_load):
        """Test loading epidemiological data."""
        mock_load.return_value = self.mock_epi_data
        
        # Load all states
        result = self.data_loader.load_epidemiological_data()
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 100
        
        # Load specific state
        result = self.data_loader.load_epidemiological_data(state="lagos")
        assert isinstance(result, pd.DataFrame)
        assert all(result['state'] == 'lagos')
        
        # Test date conversion
        assert pd.api.types.is_datetime64_any_dtype(result['date'])
    
    @patch('src.data.loaders.load_dataframe')
    def test_load_epidemiological_data_invalid_state(self, mock_load):
        """Test loading epidemiological data for invalid state."""
        mock_load.return_value = self.mock_epi_data
        
        with pytest.raises(ValueError, match="No epidemiological data found"):
            self.data_loader.load_epidemiological_data(state="nonexistent")
    
    @patch('src.data.loaders.load_dataframe')
    def test_load_demographic_data(self, mock_load):
        """Test loading demographic data."""
        mock_load.return_value = self.mock_demo_data
        
        result = self.data_loader.load_demographic_data(state="lagos")
        
        assert isinstance(result, pd.DataFrame)
        mock_load.assert_called()
    
    @patch('src.data.loaders.load_dataframe')
    def test_load_vaccination_data(self, mock_load):
        """Test loading vaccination data."""
        mock_mcv1_data = pd.DataFrame({
            'state': ['lagos'],
            'mcv1_coverage': [0.7]
        })
        
        mock_sia_data = pd.DataFrame({
            'state': ['lagos'],
            'sia_2018': [1],
            'sia_2019': [1]
        })
        
        mock_load.side_effect = [mock_mcv1_data, mock_sia_data]
        
        result = self.data_loader.load_vaccination_data(state="lagos")
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
    
    @patch.object(DataLoader, 'load_epidemiological_data')
    @patch.object(DataLoader, 'load_demographic_data')
    @patch.object(DataLoader, 'load_vaccination_data')
    @patch.object(DataLoader, 'load_age_distribution_data')
    @patch.object(DataLoader, 'load_birth_seasonality_data')
    @patch.object(DataLoader, 'load_age_at_infection_data')
    def test_load_all_state_data(self, mock_age_inf, mock_birth_seas, 
                                mock_age_dist, mock_vacc, mock_demo, mock_epi):
        """Test loading all data for a state."""
        # Setup mocks
        mock_epi.return_value = self.mock_epi_data
        mock_demo.return_value = self.mock_demo_data
        mock_vacc.return_value = pd.DataFrame()
        mock_age_dist.return_value = pd.DataFrame()
        mock_birth_seas.return_value = pd.DataFrame()
        mock_age_inf.return_value = pd.DataFrame()
        
        result = self.data_loader.load_all_state_data("lagos")
        
        assert isinstance(result, dict)
        expected_keys = [
            'epidemiological', 'demographic', 'vaccination',
            'age_distribution', 'birth_seasonality', 'age_at_infection'
        ]
        
        for key in expected_keys:
            assert key in result
            assert isinstance(result[key], pd.DataFrame)
    
    @patch.object(DataLoader, 'load_all_state_data')
    def test_validate_data_availability(self, mock_load_all):
        """Test data availability validation."""
        # Mock successful loading for lagos, failure for oyo
        def mock_load_side_effect(state):
            if state == "lagos":
                return {"epidemiological": pd.DataFrame()}
            else:
                raise ValueError("Data not found")
        
        mock_load_all.side_effect = mock_load_side_effect
        
        availability = self.data_loader.validate_data_availability(["lagos", "oyo"])
        
        assert availability["lagos"] is True
        assert availability["oyo"] is False
    
    @patch.object(DataLoader, 'load_states_and_regions')
    @patch.object(DataLoader, 'load_epidemiological_data')
    def test_get_data_summary(self, mock_epi, mock_states):
        """Test data summary generation."""
        mock_states.return_value = self.mock_states_data
        mock_epi.return_value = self.mock_epi_data
        
        summary = self.data_loader.get_data_summary()
        
        assert isinstance(summary, dict)
        assert 'states_regions' in summary
        assert 'epidemiological' in summary
        
        # Check summary structure
        states_summary = summary['states_regions']
        assert 'total_records' in states_summary
        assert states_summary['total_records'] == 3


class TestEpidemiologicalDataProcessor:
    """Test EpidemiologicalDataProcessor class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.processor = EpidemiologicalDataProcessor()
        
        # Create mock surveillance data
        self.mock_surveillance_data = pd.DataFrame({
            'state': ['lagos'] * 100,
            'date': pd.date_range('2020-01-01', periods=100),
            'age': np.random.exponential(2, 100),
            'case_status': np.random.choice(
                ['confirmed', 'rejected', 'untested'], 100, p=[0.4, 0.3, 0.3]
            ),
            'vaccine_doses': np.random.choice([0, 1, 2, np.nan], 100),
            'lab_result': ['positive'] * 40 + ['negative'] * 30 + [np.nan] * 30,
            'symptoms': ['fever_rash'] * 100
        })
    
    def test_initialization(self):
        """Test processor initialization."""
        assert hasattr(self.processor, 'logger')
        assert self.processor._case_classifier is None
    
    def test_validate_surveillance_data(self):
        """Test surveillance data validation."""
        # Valid data
        self.processor._validate_surveillance_data(self.mock_surveillance_data)
        
        # Missing required columns
        invalid_data = pd.DataFrame({'wrong_column': [1, 2, 3]})
        with pytest.raises(ValueError, match="Missing required columns"):
            self.processor._validate_surveillance_data(invalid_data)
        
        # Invalid date format
        invalid_date_data = self.mock_surveillance_data.copy()
        invalid_date_data['date'] = ['invalid_date'] * len(invalid_date_data)
        with pytest.raises(ValueError, match="Invalid date format"):
            self.processor._validate_surveillance_data(invalid_date_data)
    
    def test_create_classification_features(self):
        """Test feature creation for case classification."""
        data = self.mock_surveillance_data.copy()
        data['date'] = pd.to_datetime(data['date'])
        
        result = self.processor._create_classification_features(data)
        
        # Check new features
        assert 'age_over_5' in result.columns
        assert 'dose_1' in result.columns
        assert 'dose_2_plus' in result.columns
        assert 'dose_missing' in result.columns
        assert 'year' in result.columns
        assert 'month' in result.columns
        assert 'time_index' in result.columns
        
        # Check feature values
        assert result['age_over_5'].dtype == int
        assert all(result['age_over_5'].isin([0, 1]))
    
    def test_train_case_classifier(self):
        """Test case classifier training."""
        data = self.mock_surveillance_data.copy()
        data = self.processor._create_classification_features(data)
        
        # Ensure we have tested cases
        data.loc[:30, 'case_status'] = 'confirmed'
        data.loc[31:60, 'case_status'] = 'rejected'
        
        self.processor._train_case_classifier(data)
        
        assert self.processor._case_classifier is not None
        assert hasattr(self.processor._case_classifier, 'predict_proba')
    
    def test_apply_case_classification(self):
        """Test case classification application."""
        data = self.mock_surveillance_data.copy()
        data = self.processor._create_classification_features(data)
        
        # Train classifier first
        data.loc[:30, 'case_status'] = 'confirmed'
        data.loc[31:60, 'case_status'] = 'rejected'
        data.loc[61:, 'case_status'] = 'untested'
        
        self.processor._train_case_classifier(data)
        result = self.processor._apply_case_classification(data)
        
        # Check that untested cases have confirmation probabilities
        untested_mask = result['case_status'] == 'untested'
        assert 'confirmation_probability' in result.columns
        assert not result.loc[untested_mask, 'confirmation_probability'].isna().any()
        
        # Check expected cases column
        assert 'expected_cases' in result.columns
        confirmed_mask = result['case_status'] == 'confirmed'
        assert all(result.loc[confirmed_mask, 'expected_cases'] == 1.0)
    
    def test_aggregate_time_series(self):
        """Test time series aggregation."""
        data = self.mock_surveillance_data.copy()
        data['expected_cases'] = np.random.random(len(data))
        
        result = self.processor._aggregate_time_series(data)
        
        assert isinstance(result, pd.DataFrame)
        assert 'date' in result.columns
        assert 'cases' in result.columns
        assert 'total_reports' in result.columns
        
        # Check that all dates are present (no gaps)
        date_range = pd.date_range(
            start=result['date'].min(),
            end=result['date'].max(),
            freq='D'
        )
        assert len(result) == len(date_range)
    
    def test_add_derived_variables(self):
        """Test addition of derived variables."""
        time_series = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=100),
            'cases': np.random.poisson(5, 100),
            'total_reports': np.random.poisson(10, 100)
        })
        
        result = self.processor._add_derived_variables(time_series)
        
        # Check derived variables
        expected_columns = [
            'year', 'month', 'week', 'day_of_year',
            'cumulative_cases', 'cases_7day_ma', 'cases_30day_ma',
            'reporting_rate'
        ]
        
        for col in expected_columns:
            assert col in result.columns
        
        # Check cumulative cases
        assert result['cumulative_cases'].iloc[-1] == result['cases'].sum()
    
    def test_process_surveillance_data_integration(self):
        """Test full surveillance data processing pipeline."""
        # This is an integration test of the full processing pipeline
        result = self.processor.process_surveillance_data(
            self.mock_surveillance_data, "lagos"
        )
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) > 0
        assert 'date' in result.columns
        assert 'cases' in result.columns
        
        # Check that dates are properly formatted
        assert pd.api.types.is_datetime64_any_dtype(result['date'])
    
    def test_compute_age_distribution(self):
        """Test age distribution computation."""
        data = self.mock_surveillance_data.copy()
        data['expected_cases'] = 1.0  # All cases count as 1
        
        # Set some cases as confirmed
        data.loc[:50, 'case_status'] = 'confirmed'
        
        result = self.processor.compute_age_distribution(data)
        
        if not result.empty:  # Only test if we have confirmed cases
            assert isinstance(result, pd.DataFrame)
            assert 'age_group' in result.columns
            assert 'expected_cases' in result.columns
            assert 'proportion' in result.columns
            
            # Check proportions sum to 1
            assert abs(result['proportion'].sum() - 1.0) < 1e-6
    
    def test_estimate_reporting_rate(self):
        """Test reporting rate estimation."""
        time_series = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=365),
            'cases': np.random.poisson(5, 365),
            'year': [2020] * 365
        })
        
        expected_burden = 2000  # Expected annual burden
        
        result = self.processor.estimate_reporting_rate(time_series, expected_burden)
        
        assert isinstance(result, dict)
        assert 'reporting_rate' in result
        assert 'ci_lower' in result
        assert 'ci_upper' in result
        assert 'observed_annual_cases' in result
        assert 'expected_annual_burden' in result
        
        # Check values are reasonable
        assert 0 <= result['reporting_rate'] <= 1
        assert result['ci_lower'] <= result['reporting_rate'] <= result['ci_upper']


if __name__ == "__main__":
    pytest.main([__file__])
