"""
Unit tests for the core module of the intensification package.

This module tests the core transmission modeling classes and functionality.
"""

import pytest
import numpy as np
import pandas as pd
from unittest.mock import Mock, patch

from src.core import (
    NeighborhoodSIRModel, 
    SurvivalAnalyzer, 
    Config,
    ModelParameters,
    ModelResults,
    VaccinationOpportunity,
    CohortAnalysisResult
)


class TestModelParameters:
    """Test ModelParameters dataclass."""
    
    def test_default_parameters(self):
        """Test default parameter values."""
        params = ModelParameters()
        
        assert params.beta_correlation == 3.0
        assert params.tau == 26
        assert params.mu_guess == 0.1
    
    def test_parameter_validation(self):
        """Test parameter validation."""
        # Valid parameters
        params = ModelParameters(beta_correlation=2.0, tau=52, mu_guess=0.2)
        assert params.beta_correlation == 2.0
        
        # Invalid parameters
        with pytest.raises(ValueError):
            ModelParameters(beta_correlation=-1.0)
        
        with pytest.raises(ValueError):
            ModelParameters(tau=0)
        
        with pytest.raises(ValueError):
            ModelParameters(mu_guess=1.5)


class TestNeighborhoodSIRModel:
    """Test NeighborhoodSIRModel class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.model = NeighborhoodSIRModel()
        
        # Create test data
        self.test_data = pd.DataFrame({
            'cases': np.random.poisson(5, 100),
            'adj_births': np.random.poisson(1000, 100),
            'adj_cases_p': np.random.poisson(5, 100)
        })
        
        self.sia_effects = np.random.random((100, 3)) * 1000
        self.s0_prior = 10000
        self.s0_var = 1000
    
    def test_initialization(self):
        """Test model initialization."""
        assert isinstance(self.model.parameters, ModelParameters)
        assert not self.model.is_fitted()
        assert self.model.get_results() is None
    
    def test_data_validation(self):
        """Test input data validation."""
        # Valid data
        self.model.validate_data(self.test_data)
        
        # Invalid data - empty DataFrame
        with pytest.raises(ValueError):
            self.model.validate_data(pd.DataFrame())
        
        # Invalid data - not a DataFrame
        with pytest.raises(TypeError):
            self.model.validate_data("not a dataframe")
    
    def test_fit_input_validation(self):
        """Test fit method input validation."""
        # Missing required columns
        invalid_data = pd.DataFrame({'wrong_column': [1, 2, 3]})
        
        with pytest.raises(ValueError):
            self.model._validate_fit_inputs(
                invalid_data, self.sia_effects, self.s0_prior, self.s0_var
            )
        
        # Invalid SIA effects shape
        with pytest.raises(ValueError):
            self.model._validate_fit_inputs(
                self.test_data, np.array([1, 2, 3]), self.s0_prior, self.s0_var
            )
        
        # Invalid priors
        with pytest.raises(ValueError):
            self.model._validate_fit_inputs(
                self.test_data, self.sia_effects, -1, self.s0_var
            )
    
    def test_setup_model(self):
        """Test model setup."""
        self.model._data = self.test_data
        self.model._sia_effects = self.sia_effects
        self.model._setup_model(self.s0_prior, self.s0_var)
        
        assert self.model.T == len(self.test_data) - 1
        assert self.model.num_sias == self.sia_effects.shape[1]
        assert hasattr(self.model, 'pRW2')
        assert hasattr(self.model, 'X')
        assert hasattr(self.model, 'C')
        assert hasattr(self.model, 'H')
    
    def test_compute_compartments(self):
        """Test compartment computation."""
        self.model._data = self.test_data
        self.model._sia_effects = self.sia_effects
        self.model._setup_model(self.s0_prior, self.s0_var)
        
        log_s0 = np.log(self.s0_prior)
        mu = np.array([0.1, 0.2, 0.15])
        
        S_t, I_t, E_t = self.model._compute_compartments(log_s0, mu)
        
        assert len(S_t) == len(I_t) == len(E_t)
        assert all(S_t >= 0)  # Susceptible population should be non-negative
        assert all(I_t >= 0)  # Infectious population should be non-negative
    
    def test_log_likelihood_computation(self):
        """Test log-likelihood computation."""
        self.model._data = self.test_data
        self.model._sia_effects = self.sia_effects
        self.model._setup_model(self.s0_prior, self.s0_var)
        
        theta = np.array([np.log(self.s0_prior), 0.1, 0.2, 0.15])
        
        ll = self.model.log_likelihood(theta)
        
        assert isinstance(ll, float)
        assert not np.isnan(ll)
        assert not np.isinf(ll)
    
    @patch('src.core.transmission_model.minimize')
    def test_fit_method(self, mock_minimize):
        """Test model fitting method."""
        # Mock optimization result
        mock_result = Mock()
        mock_result.success = True
        mock_result.x = np.array([np.log(self.s0_prior), 0.1, 0.2, 0.15])
        mock_result.fun = -100.0
        mock_result.message = "Optimization terminated successfully"
        mock_result.nit = 50
        mock_result.nfev = 200
        mock_minimize.return_value = mock_result
        
        # Fit model
        results = self.model.fit(
            data=self.test_data,
            sia_effects=self.sia_effects,
            s0_prior=self.s0_prior,
            s0_var=self.s0_var
        )
        
        assert isinstance(results, ModelResults)
        assert self.model.is_fitted()
        assert results.log_likelihood == 100.0  # Negative of mock_result.fun
        assert results.convergence_info['success']
    
    def test_predict_without_fitting(self):
        """Test prediction without fitting raises error."""
        with pytest.raises(RuntimeError):
            self.model.predict(np.array([1, 2, 3]))


class TestSurvivalAnalyzer:
    """Test SurvivalAnalyzer class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.analyzer = SurvivalAnalyzer()
        
        # Create test vaccination opportunity
        self.vacc_opp = VaccinationOpportunity(
            name="MCV1",
            time_months=9.0,
            efficacy=0.825,
            coverage_func=lambda year: 0.7
        )
        
        # Mock age-at-infection model
        self.age_infection_model = lambda age, year: np.exp(-age/24) / 24
    
    def test_initialization(self):
        """Test analyzer initialization."""
        assert len(self.analyzer._vaccination_opportunities) == 0
        assert self.analyzer._age_infection_model is None
    
    def test_add_vaccination_opportunity(self):
        """Test adding vaccination opportunities."""
        self.analyzer.add_vaccination_opportunity(self.vacc_opp)
        
        assert len(self.analyzer._vaccination_opportunities) == 1
        assert self.analyzer._vaccination_opportunities[0].name == "MCV1"
    
    def test_vaccination_opportunity_validation(self):
        """Test vaccination opportunity validation."""
        # Invalid efficacy
        with pytest.raises(ValueError):
            VaccinationOpportunity(
                name="Invalid",
                time_months=9.0,
                efficacy=1.5,  # Invalid
                coverage_func=lambda year: 0.7
            )
        
        # Invalid time
        with pytest.raises(ValueError):
            VaccinationOpportunity(
                name="Invalid",
                time_months=-1.0,  # Invalid
                efficacy=0.8,
                coverage_func=lambda year: 0.7
            )
    
    def test_set_age_infection_model(self):
        """Test setting age-at-infection model."""
        self.analyzer.set_age_infection_model(self.age_infection_model)
        
        assert self.analyzer._age_infection_model is not None
        
        # Test model function
        prob = self.analyzer._age_infection_model(12, 2010)
        assert isinstance(prob, float)
        assert prob > 0
    
    def test_analyze_cohort(self):
        """Test cohort analysis."""
        # Setup analyzer
        self.analyzer.add_vaccination_opportunity(self.vacc_opp)
        self.analyzer.set_age_infection_model(self.age_infection_model)
        
        # Analyze cohort
        result = self.analyzer.analyze_cohort(birth_year=2010, cohort_size=1000)
        
        assert isinstance(result, CohortAnalysisResult)
        assert result.cohort_year == 2010
        assert result.cohort_size == 1000
        assert 0 <= result.infection_fraction <= 1
        assert "MCV1" in result.immunity_fractions
        
        # Check normalization
        total_fraction = result.infection_fraction + sum(result.immunity_fractions.values())
        assert abs(total_fraction - 1.0) < 1e-3
    
    def test_analyze_cohort_without_setup(self):
        """Test cohort analysis without proper setup."""
        # No vaccination opportunities
        with pytest.raises(ValueError):
            self.analyzer.analyze_cohort(birth_year=2010, cohort_size=1000)
        
        # No age-infection model
        self.analyzer.add_vaccination_opportunity(self.vacc_opp)
        with pytest.raises(ValueError):
            self.analyzer.analyze_cohort(birth_year=2010, cohort_size=1000)
    
    def test_estimate_annual_burden(self):
        """Test annual burden estimation."""
        # Create mock cohort results
        results = [
            CohortAnalysisResult(
                cohort_year=2010,
                cohort_size=1000,
                immunity_fractions={"MCV1": 0.7},
                infection_fraction=0.3,
                age_infection_dist=np.array([0.1, 0.1, 0.1])
            ),
            CohortAnalysisResult(
                cohort_year=2011,
                cohort_size=1000,
                immunity_fractions={"MCV1": 0.8},
                infection_fraction=0.2,
                age_infection_dist=np.array([0.05, 0.1, 0.05])
            )
        ]
        
        burden_df = self.analyzer.estimate_annual_burden(results)
        
        assert isinstance(burden_df, pd.DataFrame)
        assert 'year' in burden_df.columns
        assert 'expected_annual_cases' in burden_df.columns
        assert len(burden_df) > 0
    
    def test_default_vaccination_schedule(self):
        """Test creation of default vaccination schedule."""
        self.analyzer.create_default_vaccination_schedule()
        
        assert len(self.analyzer._vaccination_opportunities) == 2
        
        # Check MCV1 and MCV2 are present
        names = [opp.name for opp in self.analyzer._vaccination_opportunities]
        assert "MCV1" in names
        assert "MCV2" in names


class TestConfig:
    """Test Config class."""
    
    def test_default_config(self):
        """Test default configuration."""
        config = Config()
        
        assert config.data_paths.data_dir == Path("_data")
        assert config.model.beta_correlation == 3.0
        assert config.analysis.default_state == "lagos"
        assert config.random_seed == 42
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Valid config
        config = Config()
        assert config.model.beta_correlation > 0
        
        # Test that Pydantic validation works
        with pytest.raises(ValueError):
            Config(model={"beta_correlation": -1.0})
    
    def test_ensure_directories(self):
        """Test directory creation."""
        config = Config()
        
        # This should not raise an error
        config.ensure_directories()
        
        # Check that directories exist
        assert config.data_paths.data_dir.exists()
        assert config.data_paths.plots_dir.exists()
        assert config.data_paths.pickle_dir.exists()


if __name__ == "__main__":
    pytest.main([__file__])
