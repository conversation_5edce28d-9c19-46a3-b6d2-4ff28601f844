"""
Unit tests for the utils module of the intensification package.

This module tests utility functions, mathematical operations, and helper functions.
"""

import pytest
import numpy as np
import pandas as pd
from pathlib import Path
import tempfile
import logging
from unittest.mock import patch, Mock

from src.utils import (
    setup_logging,
    get_logger,
    low_mid_high,
    compute_confidence_intervals,
    fit_quality_metrics,
    save_pickle,
    load_pickle,
    save_dataframe,
    load_dataframe,
    ensure_directory
)
from src.utils.constants import (
    STATES_SOUTHERN_NIGERIA,
    validate_state,
    validate_year,
    get_state_color,
    COLORS
)


class TestLoggingUtils:
    """Test logging utilities."""
    
    def test_setup_logging_default(self):
        """Test default logging setup."""
        setup_logging()
        
        # Check that root logger is configured
        root_logger = logging.getLogger()
        assert root_logger.level == logging.INFO
        assert len(root_logger.handlers) > 0
    
    def test_setup_logging_custom_level(self):
        """Test logging setup with custom level."""
        setup_logging(level="DEBUG")
        
        root_logger = logging.getLogger()
        assert root_logger.level == logging.DEBUG
    
    def test_setup_logging_invalid_level(self):
        """Test logging setup with invalid level."""
        with pytest.raises(ValueError):
            setup_logging(level="INVALID_LEVEL")
    
    def test_get_logger(self):
        """Test logger retrieval."""
        logger = get_logger("test_module")
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == "test_module"
    
    def test_setup_logging_with_file(self):
        """Test logging setup with file output."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "test.log"
            
            setup_logging(level="INFO", log_file=log_file)
            
            # Test that file handler is added
            root_logger = logging.getLogger()
            file_handlers = [h for h in root_logger.handlers 
                           if isinstance(h, logging.FileHandler)]
            assert len(file_handlers) > 0
            
            # Test logging to file
            logger = get_logger("test")
            logger.info("Test message")
            
            assert log_file.exists()


class TestMathUtils:
    """Test mathematical utility functions."""
    
    def test_low_mid_high_single_confidence(self):
        """Test confidence interval computation with single level."""
        # Create test samples
        samples = np.random.normal(0, 1, (1000, 10))
        
        result = low_mid_high(samples, confidence_levels=[0.95])
        
        assert len(result) == 3  # low, median, high
        low, median, high = result
        
        assert len(low) == 10
        assert len(median) == 10
        assert len(high) == 10
        
        # Check ordering
        assert np.all(low <= median)
        assert np.all(median <= high)
    
    def test_low_mid_high_multiple_confidence(self):
        """Test confidence interval computation with multiple levels."""
        samples = np.random.normal(0, 1, (1000, 10))
        
        result = low_mid_high(samples, confidence_levels=[0.5, 0.95])
        
        assert len(result) == 5  # low_95, low_50, median, high_50, high_95
        low_95, low_50, median, high_50, high_95 = result
        
        # Check ordering
        assert np.all(low_95 <= low_50)
        assert np.all(low_50 <= median)
        assert np.all(median <= high_50)
        assert np.all(high_50 <= high_95)
    
    def test_low_mid_high_1d_array(self):
        """Test confidence intervals with 1D array."""
        samples = np.random.normal(0, 1, 1000)
        
        result = low_mid_high(samples, confidence_levels=[0.95])
        
        assert len(result) == 3
        low, median, high = result
        
        assert isinstance(low, (float, np.floating))
        assert isinstance(median, (float, np.floating))
        assert isinstance(high, (float, np.floating))
        assert low <= median <= high
    
    def test_compute_confidence_intervals(self):
        """Test confidence interval computation."""
        data = np.random.normal(0, 1, (100, 10))
        
        lower, upper = compute_confidence_intervals(data, confidence_level=0.95)
        
        assert len(lower) == 10
        assert len(upper) == 10
        assert np.all(lower <= upper)
    
    def test_compute_confidence_intervals_invalid_level(self):
        """Test confidence intervals with invalid level."""
        data = np.random.normal(0, 1, (100, 10))
        
        with pytest.raises(ValueError):
            compute_confidence_intervals(data, confidence_level=1.5)
        
        with pytest.raises(ValueError):
            compute_confidence_intervals(data, confidence_level=0.0)
    
    def test_fit_quality_metrics(self):
        """Test model fit quality metrics."""
        # Create test data
        observed = np.array([1, 2, 3, 4, 5])
        predicted = np.array([1.1, 1.9, 3.1, 3.9, 5.1])
        
        metrics = fit_quality_metrics(observed, predicted, verbose=False)
        
        assert isinstance(metrics, dict)
        assert 'r2_score' in metrics
        assert 'mae' in metrics
        assert 'rmse' in metrics
        assert 'mape' in metrics
        
        # Check metric values are reasonable
        assert 0 <= metrics['r2_score'] <= 1
        assert metrics['mae'] >= 0
        assert metrics['rmse'] >= 0
        assert metrics['mape'] >= 0
    
    def test_fit_quality_metrics_with_intervals(self):
        """Test fit quality metrics with prediction intervals."""
        observed = np.array([1, 2, 3, 4, 5])
        predicted = np.array([1.1, 1.9, 3.1, 3.9, 5.1])
        lower = np.array([0.5, 1.5, 2.5, 3.5, 4.5])
        upper = np.array([1.5, 2.5, 3.5, 4.5, 5.5])
        
        metrics = fit_quality_metrics(
            observed, predicted, 
            prediction_intervals=(lower, upper),
            verbose=False
        )
        
        assert 'interval_coverage' in metrics
        assert 'n_within_interval' in metrics
        assert 'n_total' in metrics
        
        assert 0 <= metrics['interval_coverage'] <= 1
        assert metrics['n_total'] == len(observed)


class TestFileUtils:
    """Test file utility functions."""
    
    def test_ensure_directory(self):
        """Test directory creation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = Path(temp_dir) / "test" / "nested" / "directory"
            
            result = ensure_directory(test_dir)
            
            assert result == test_dir
            assert test_dir.exists()
            assert test_dir.is_dir()
    
    def test_save_load_pickle(self):
        """Test pickle save and load operations."""
        test_data = {"key": "value", "numbers": [1, 2, 3]}
        
        with tempfile.TemporaryDirectory() as temp_dir:
            pickle_path = Path(temp_dir) / "test.pkl"
            
            # Save pickle
            save_pickle(test_data, pickle_path)
            assert pickle_path.exists()
            
            # Load pickle
            loaded_data = load_pickle(pickle_path)
            assert loaded_data == test_data
    
    def test_load_pickle_nonexistent(self):
        """Test loading nonexistent pickle file."""
        with pytest.raises(FileNotFoundError):
            load_pickle("nonexistent_file.pkl")
    
    def test_save_load_dataframe_csv(self):
        """Test DataFrame save and load operations (CSV)."""
        test_df = pd.DataFrame({
            'A': [1, 2, 3],
            'B': ['a', 'b', 'c'],
            'C': [1.1, 2.2, 3.3]
        })
        
        with tempfile.TemporaryDirectory() as temp_dir:
            csv_path = Path(temp_dir) / "test.csv"
            
            # Save DataFrame
            save_dataframe(test_df, csv_path, format='csv', index=False)
            assert csv_path.exists()
            
            # Load DataFrame
            loaded_df = load_dataframe(csv_path, format='csv')
            pd.testing.assert_frame_equal(test_df, loaded_df)
    
    def test_save_dataframe_invalid_format(self):
        """Test saving DataFrame with invalid format."""
        test_df = pd.DataFrame({'A': [1, 2, 3]})
        
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = Path(temp_dir) / "test.invalid"
            
            with pytest.raises(ValueError):
                save_dataframe(test_df, file_path, format='invalid_format')
    
    def test_load_dataframe_nonexistent(self):
        """Test loading nonexistent DataFrame file."""
        with pytest.raises(FileNotFoundError):
            load_dataframe("nonexistent_file.csv")


class TestConstants:
    """Test constants and validation functions."""
    
    def test_states_list(self):
        """Test southern states list."""
        assert isinstance(STATES_SOUTHERN_NIGERIA, list)
        assert len(STATES_SOUTHERN_NIGERIA) == 17
        assert 'lagos' in STATES_SOUTHERN_NIGERIA
        assert 'oyo' in STATES_SOUTHERN_NIGERIA
    
    def test_validate_state_valid(self):
        """Test state validation with valid states."""
        # Test exact match
        assert validate_state('lagos') == 'lagos'
        
        # Test case insensitive
        assert validate_state('LAGOS') == 'lagos'
        assert validate_state('Lagos') == 'lagos'
        
        # Test with spaces
        assert validate_state('akwa ibom') == 'akwa_ibom'
        assert validate_state('cross river') == 'cross_river'
    
    def test_validate_state_invalid(self):
        """Test state validation with invalid states."""
        with pytest.raises(ValueError, match="Invalid state name"):
            validate_state('invalid_state')
        
        with pytest.raises(ValueError, match="Invalid state name"):
            validate_state('kano')  # Northern state
    
    def test_validate_year_valid(self):
        """Test year validation with valid years."""
        assert validate_year(2010) == 2010
        assert validate_year(2020) == 2020
        assert validate_year(2024) == 2024
    
    def test_validate_year_invalid(self):
        """Test year validation with invalid years."""
        with pytest.raises(ValueError, match="Invalid year"):
            validate_year(2000)  # Too early
        
        with pytest.raises(ValueError, match="Invalid year"):
            validate_year(2030)  # Too late
    
    def test_get_state_color(self):
        """Test state color assignment."""
        color = get_state_color('lagos')
        
        assert isinstance(color, str)
        assert color.startswith('#')
        assert len(color) == 7  # Hex color format
        
        # Test that same state gets same color
        assert get_state_color('lagos') == get_state_color('lagos')
    
    def test_get_state_color_invalid(self):
        """Test state color with invalid state."""
        with pytest.raises(ValueError):
            get_state_color('invalid_state')
    
    def test_colors_list(self):
        """Test colors list."""
        assert isinstance(COLORS, list)
        assert len(COLORS) > 0
        
        # Check all colors are valid hex codes
        for color in COLORS:
            assert isinstance(color, str)
            assert color.startswith('#')
            assert len(color) == 7


class TestIntegration:
    """Integration tests for utility functions."""
    
    def test_logging_and_file_operations(self):
        """Test integration of logging and file operations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "test.log"
            
            # Setup logging
            setup_logging(level="DEBUG", log_file=log_file)
            logger = get_logger("test_integration")
            
            # Test file operations with logging
            test_data = pd.DataFrame({'A': [1, 2, 3]})
            data_file = Path(temp_dir) / "data.csv"
            
            logger.info("Saving test data")
            save_dataframe(test_data, data_file)
            
            logger.info("Loading test data")
            loaded_data = load_dataframe(data_file)
            
            # Verify operations
            assert data_file.exists()
            assert log_file.exists()
            pd.testing.assert_frame_equal(test_data, loaded_data)
    
    def test_math_utils_with_real_data(self):
        """Test math utilities with realistic data."""
        # Simulate model predictions vs observations
        np.random.seed(42)
        true_values = np.sin(np.linspace(0, 4*np.pi, 100)) + np.random.normal(0, 0.1, 100)
        
        # Simulate multiple model runs
        n_runs = 1000
        predictions = np.array([
            true_values + np.random.normal(0, 0.2, len(true_values))
            for _ in range(n_runs)
        ])
        
        # Compute confidence intervals
        low_95, low_50, median, high_50, high_95 = low_mid_high(
            predictions, confidence_levels=[0.5, 0.95]
        )
        
        # Compute fit quality
        metrics = fit_quality_metrics(
            true_values, median,
            prediction_intervals=(low_95, high_95),
            verbose=False
        )
        
        # Check results are reasonable
        assert metrics['r2_score'] > 0.5  # Should have decent fit
        assert 0.8 < metrics['interval_coverage'] < 1.0  # Good coverage
        assert metrics['mae'] < 1.0  # Reasonable error


if __name__ == "__main__":
    pytest.main([__file__])
