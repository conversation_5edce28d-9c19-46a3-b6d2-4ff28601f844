"""
Pytest configuration and fixtures for the intensification package tests.

This module provides shared fixtures and configuration for all tests.
"""

import pytest
import numpy as np
import pandas as pd
from pathlib import Path
import tempfile
from unittest.mock import Mock

from src.core import Config, ModelParameters
from src.data import DataLoader


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_config():
    """Create a sample configuration for testing."""
    return Config()


@pytest.fixture
def sample_model_parameters():
    """Create sample model parameters for testing."""
    return ModelParameters(
        beta_correlation=3.0,
        tau=26,
        mu_guess=0.1
    )


@pytest.fixture
def sample_epidemiological_data():
    """Create sample epidemiological data for testing."""
    np.random.seed(42)  # For reproducibility
    
    dates = pd.date_range('2020-01-01', periods=365, freq='D')
    
    # Simulate seasonal pattern
    day_of_year = dates.dayofyear
    seasonal_effect = 1 + 0.3 * np.sin(2 * np.pi * day_of_year / 365.25)
    
    # Generate case counts
    base_rate = 2.0
    expected_cases = base_rate * seasonal_effect
    cases = np.random.poisson(expected_cases)
    
    return pd.DataFrame({
        'date': dates,
        'cases': cases,
        'state': 'lagos',
        'adj_births': np.random.poisson(1000, len(dates)),
        'adj_cases_p': cases
    })


@pytest.fixture
def sample_surveillance_data():
    """Create sample surveillance data for testing."""
    np.random.seed(42)
    
    n_records = 1000
    
    return pd.DataFrame({
        'state': ['lagos'] * n_records,
        'date': pd.date_range('2020-01-01', periods=n_records, freq='D'),
        'age': np.random.exponential(2.5, n_records),
        'case_status': np.random.choice(
            ['confirmed', 'rejected', 'untested', 'epidemiologically_linked'], 
            n_records, 
            p=[0.3, 0.2, 0.4, 0.1]
        ),
        'vaccine_doses': np.random.choice([0, 1, 2, np.nan], n_records, p=[0.3, 0.4, 0.2, 0.1]),
        'lab_result': np.random.choice(['positive', 'negative', np.nan], n_records, p=[0.3, 0.2, 0.5]),
        'symptoms': ['fever_rash'] * n_records
    })


@pytest.fixture
def sample_demographic_data():
    """Create sample demographic data for testing."""
    return pd.DataFrame({
        'state': ['lagos', 'oyo', 'rivers'],
        'population': [15000000, 8000000, 6000000],
        'birth_rate': [28.5, 30.2, 32.1],
        'monthly_births': [35000, 20000, 16000]
    })


@pytest.fixture
def sample_vaccination_data():
    """Create sample vaccination data for testing."""
    return pd.DataFrame({
        'state': ['lagos', 'oyo', 'rivers'],
        'mcv1_coverage_2020': [0.75, 0.68, 0.72],
        'mcv1_coverage_2021': [0.78, 0.70, 0.74],
        'mcv2_coverage_2020': [0.45, 0.38, 0.42],
        'sia_2018': [1, 1, 1],
        'sia_2019': [1, 0, 1],
        'sia_2022': [1, 1, 1]
    })


@pytest.fixture
def sample_sia_effects():
    """Create sample SIA effects matrix for testing."""
    np.random.seed(42)
    
    n_periods = 100
    n_campaigns = 5
    
    # Create sparse matrix with campaigns at specific periods
    sia_effects = np.zeros((n_periods, n_campaigns))
    
    campaign_periods = [20, 40, 60, 80, 95]
    campaign_sizes = [50000, 75000, 60000, 80000, 45000]
    
    for i, (period, size) in enumerate(zip(campaign_periods, campaign_sizes)):
        if period < n_periods:
            sia_effects[period, i] = size
    
    return sia_effects


@pytest.fixture
def sample_time_series():
    """Create sample time series data for model fitting."""
    np.random.seed(42)
    
    n_periods = 100
    
    return pd.DataFrame({
        'cases': np.random.poisson(5, n_periods),
        'adj_births': np.random.poisson(1000, n_periods),
        'adj_cases_p': np.random.poisson(5, n_periods),
        'period': range(n_periods)
    })


@pytest.fixture
def mock_data_loader():
    """Create a mock data loader for testing."""
    loader = Mock(spec=DataLoader)
    
    # Configure mock methods
    loader.load_epidemiological_data.return_value = pd.DataFrame({
        'date': pd.date_range('2020-01-01', periods=100),
        'cases': np.random.poisson(5, 100),
        'state': ['lagos'] * 100
    })
    
    loader.load_demographic_data.return_value = pd.DataFrame({
        'state': ['lagos'],
        'population': [15000000],
        'birth_rate': [28.5]
    })
    
    loader.load_vaccination_data.return_value = pd.DataFrame({
        'state': ['lagos'],
        'mcv1_coverage': [0.75]
    })
    
    loader.validate_data_availability.return_value = {'lagos': True}
    
    return loader


@pytest.fixture
def sample_cohort_results():
    """Create sample cohort analysis results for testing."""
    from src.core.survival_analysis import CohortAnalysisResult
    
    results = []
    
    for year in range(2010, 2015):
        result = CohortAnalysisResult(
            cohort_year=year,
            cohort_size=1000,
            immunity_fractions={
                'MCV1': 0.7,
                'MCV2': 0.4,
                'Campaign_2013': 0.1 if year <= 2013 else 0.0
            },
            infection_fraction=0.2,
            age_infection_dist=np.random.exponential(0.1, 60)  # 5 years in months
        )
        results.append(result)
    
    return results


@pytest.fixture(autouse=True)
def reset_random_seed():
    """Reset random seed before each test for reproducibility."""
    np.random.seed(42)


@pytest.fixture(scope="session")
def test_data_dir():
    """Create test data directory with sample files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        data_dir = Path(temp_dir) / "_data"
        data_dir.mkdir()
        
        # Create sample data files
        states_data = pd.DataFrame({
            'state': ['lagos', 'oyo', 'rivers'],
            'region': ['south_west', 'south_west', 'south_south']
        })
        states_data.to_csv(data_dir / "states_and_regions.csv")
        
        # Create other sample files
        epi_data = pd.DataFrame({
            'state': ['lagos'] * 100,
            'date': pd.date_range('2020-01-01', periods=100),
            'cases': np.random.poisson(5, 100)
        })
        epi_data.to_csv(data_dir / "southern_states_epi_timeseries.csv", index=False)
        
        yield data_dir


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location."""
    for item in items:
        # Mark integration tests
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        else:
            item.add_marker(pytest.mark.unit)
        
        # Mark slow tests
        if "slow" in item.name or "integration" in item.nodeid:
            item.add_marker(pytest.mark.slow)


# Custom assertions
def assert_dataframe_equal_ignore_index(df1, df2):
    """Assert DataFrames are equal ignoring index."""
    pd.testing.assert_frame_equal(
        df1.reset_index(drop=True), 
        df2.reset_index(drop=True)
    )


def assert_array_close(arr1, arr2, rtol=1e-5, atol=1e-8):
    """Assert arrays are close with custom tolerances."""
    np.testing.assert_allclose(arr1, arr2, rtol=rtol, atol=atol)


# Add custom assertions to pytest namespace
pytest.assert_dataframe_equal_ignore_index = assert_dataframe_equal_ignore_index
pytest.assert_array_close = assert_array_close
