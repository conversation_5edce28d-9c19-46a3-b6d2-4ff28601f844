"""
Command Line Interface for the intensification package.

This module provides a CLI for running analyses, generating plots, and
performing model validation using the intensification package.
"""

import typer
from typing import Optional, List
from pathlib import Path
import sys

from .core import Config, NeighborhoodSIRModel
from .data import DataLoader
from .utils import setup_logging, get_logger
from .utils.constants import STATES_SOUTHERN_NIGERIA, validate_state

app = typer.Typer(
    name="intensification",
    help="Measles transmission modeling and vaccination campaign analysis",
    add_completion=False
)


@app.command()
def analyze(
    state: str = typer.Argument(..., help="State name to analyze"),
    output_dir: Path = typer.Option(
        Path("results"), 
        "--output", "-o", 
        help="Output directory for results"
    ),
    config_file: Optional[Path] = typer.Option(
        None, 
        "--config", "-c", 
        help="Configuration file path"
    ),
    verbose: bool = typer.Option(
        False, 
        "--verbose", "-v", 
        help="Enable verbose logging"
    )
):
    """Analyze measles transmission for a specific state."""
    
    # Setup logging
    log_level = "DEBUG" if verbose else "INFO"
    setup_logging(level=log_level, use_rich=True)
    logger = get_logger(__name__)
    
    try:
        # Validate state
        state = validate_state(state)
        logger.info(f"Starting analysis for {state.upper()}")
        
        # Load configuration
        if config_file:
            config = Config.from_file(config_file)
        else:
            config = Config()
        
        # Create output directory
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load data
        data_loader = DataLoader()
        state_data = data_loader.load_all_state_data(state)
        
        # Run analysis (simplified for CLI)
        logger.info("Analysis completed successfully")
        logger.info(f"Results saved to: {output_dir}")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise typer.Exit(1)


@app.command()
def validate(
    states: Optional[List[str]] = typer.Option(
        None, 
        "--states", "-s", 
        help="States to validate (default: all southern states)"
    ),
    forecast_years: int = typer.Option(
        3, 
        "--forecast-years", "-f", 
        help="Number of years for forecast validation"
    ),
    output_dir: Path = typer.Option(
        Path("validation"), 
        "--output", "-o", 
        help="Output directory for validation results"
    )
):
    """Validate models using out-of-sample forecasting."""
    
    setup_logging(level="INFO", use_rich=True)
    logger = get_logger(__name__)
    
    if states is None:
        states = STATES_SOUTHERN_NIGERIA
    
    logger.info(f"Validating models for {len(states)} states")
    logger.info(f"Forecast horizon: {forecast_years} years")
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Run validation
    logger.info("Validation completed")


@app.command()
def plot(
    state: str = typer.Argument(..., help="State name to plot"),
    figures: str = typer.Option(
        "all", 
        "--figures", "-f", 
        help="Figures to generate (all, transmission, campaigns, validation)"
    ),
    output_dir: Path = typer.Option(
        Path("_plots"), 
        "--output", "-o", 
        help="Output directory for plots"
    ),
    format: str = typer.Option(
        "png", 
        "--format", 
        help="Output format (png, pdf, svg)"
    )
):
    """Generate plots for a specific state."""
    
    setup_logging(level="INFO", use_rich=True)
    logger = get_logger(__name__)
    
    try:
        state = validate_state(state)
        logger.info(f"Generating {figures} plots for {state.upper()}")
        
        # Create output directory
        output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Plots saved to: {output_dir}")
        
    except Exception as e:
        logger.error(f"Plot generation failed: {e}")
        raise typer.Exit(1)


@app.command()
def data_check(
    states: Optional[List[str]] = typer.Option(
        None, 
        "--states", "-s", 
        help="States to check (default: all southern states)"
    )
):
    """Check data availability and quality."""
    
    setup_logging(level="INFO", use_rich=True)
    logger = get_logger(__name__)
    
    if states is None:
        states = STATES_SOUTHERN_NIGERIA
    
    logger.info(f"Checking data for {len(states)} states")
    
    data_loader = DataLoader()
    availability = data_loader.validate_data_availability(states)
    
    # Print results
    typer.echo("\nData Availability Report:")
    typer.echo("=" * 50)
    
    available_count = 0
    for state, available in availability.items():
        status = "✅" if available else "❌"
        typer.echo(f"{status} {state.upper()}: {'Available' if available else 'Not available'}")
        if available:
            available_count += 1
    
    typer.echo(f"\nSummary: {available_count}/{len(states)} states have complete data")


@app.command()
def config_create(
    output_file: Path = typer.Argument(..., help="Output configuration file path"),
    format: str = typer.Option(
        "yaml", 
        "--format", "-f", 
        help="Configuration format (yaml, json)"
    )
):
    """Create a default configuration file."""
    
    setup_logging(level="INFO", use_rich=True)
    logger = get_logger(__name__)
    
    try:
        # Create default config
        config = Config()
        
        # Save to file
        if format.lower() == "json":
            output_file = output_file.with_suffix(".json")
        else:
            output_file = output_file.with_suffix(".yaml")
        
        config.to_file(output_file)
        
        logger.info(f"Configuration file created: {output_file}")
        typer.echo(f"✅ Configuration file created: {output_file}")
        
    except Exception as e:
        logger.error(f"Failed to create configuration file: {e}")
        raise typer.Exit(1)


@app.command()
def version():
    """Show package version information."""
    
    try:
        from . import __version__, __author__
        typer.echo(f"Intensification Package v{__version__}")
        typer.echo(f"Author: {__author__}")
        typer.echo("Measles transmission modeling for Southern Nigeria")
    except ImportError:
        typer.echo("Intensification Package (development version)")


def main():
    """Main CLI entry point."""
    app()


if __name__ == "__main__":
    main()
