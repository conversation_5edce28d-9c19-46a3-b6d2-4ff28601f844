"""
Constants and configuration values for the intensification package.

This module defines constants used throughout the package, including
color schemes, state lists, and other fixed values.
"""

from typing import List, Dict

# Color palette for plots (matching original research code)
COLORS = [
    "#375E97",  # Blue
    "#FB6542",  # Orange-red
    "#FFBB00",  # Yellow
    "#5ca904",  # Green
    "#FF6B6B",  # Light red
    "#4ECDC4",  # Teal
    "#45B7D1",  # Light blue
    "#96CEB4",  # Light green
    "#FFEAA7",  # Light yellow
    "#DDA0DD",  # Plum
]

# Extended color palette for multiple states
EXTENDED_COLORS = COLORS + [
    "#8E44AD",  # Purple
    "#E67E22",  # Orange
    "#2ECC71",  # Emerald
    "#F39C12",  # Orange
    "#E74C3C",  # Red
    "#9B59B6",  # Amethyst
    "#1ABC9C",  # Turquoise
]

# Southern Nigerian states (as used in the research)
STATES_SOUTHERN_NIGERIA = [
    "abia",
    "akwa_ibom", 
    "anambra",
    "bayelsa",
    "cross_river",
    "delta",
    "ebonyi",
    "edo",
    "ekiti",
    "enugu",
    "imo",
    "lagos",
    "ogun",
    "ondo",
    "osun",
    "oyo",
    "rivers"
]

# State display names (proper capitalization)
STATE_DISPLAY_NAMES = {
    "abia": "Abia",
    "akwa_ibom": "Akwa Ibom",
    "anambra": "Anambra", 
    "bayelsa": "Bayelsa",
    "cross_river": "Cross River",
    "delta": "Delta",
    "ebonyi": "Ebonyi",
    "edo": "Edo",
    "ekiti": "Ekiti",
    "enugu": "Enugu",
    "imo": "Imo",
    "lagos": "Lagos",
    "ogun": "Ogun",
    "ondo": "Ondo",
    "osun": "Osun",
    "oyo": "Oyo",
    "rivers": "Rivers"
}

# Geopolitical zones
GEOPOLITICAL_ZONES = {
    "south_south": ["akwa_ibom", "bayelsa", "cross_river", "delta", "edo", "rivers"],
    "south_east": ["abia", "anambra", "ebonyi", "enugu", "imo"],
    "south_west": ["ekiti", "lagos", "ogun", "ondo", "osun", "oyo"]
}

# Vaccination campaign timeline (major campaigns in Southern Nigeria)
VACCINATION_CAMPAIGNS = {
    2010: {"name": "2010 Campaign", "target_age": "9m-5y"},
    2013: {"name": "2013 Campaign", "target_age": "9m-5y"},
    2014: {"name": "2014 Campaign", "target_age": "9m-5y"},
    2018: {"name": "2018 Campaign", "target_age": "9m-5y"},
    2019: {"name": "2019 IRI", "target_age": "9m-2y"},
    2022: {"name": "2022 Campaign", "target_age": "9m-5y"}
}

# Vaccine efficacy parameters (from research literature)
VACCINE_EFFICACY = {
    "mcv1": 0.825,  # First dose measles vaccine
    "mcv2": 0.95,   # Second dose measles vaccine  
    "sia": 0.9      # Supplementary immunization activity
}

# Age parameters
AGE_PARAMETERS = {
    "maternal_protection_months": 6.0,
    "mcv1_age_months": 9.0,
    "mcv2_age_months": 15.0,
    "max_analysis_age_years": 25
}

# Time parameters
TIME_PARAMETERS = {
    "analysis_start_year": 2009,
    "analysis_end_year": 2024,
    "seasonality_periods": 26,  # Semi-monthly periods per year
    "forecast_horizon_years": 3
}

# Model parameters
MODEL_PARAMETERS = {
    "beta_correlation": 3.0,
    "tau": 26,
    "mu_initial_guess": 0.1,
    "convergence_tolerance": 1e-6,
    "max_optimization_iterations": 1000
}

# File naming conventions
FILE_SUFFIXES = {
    "new_implementation": "_new",
    "backup": "_backup",
    "temp": "_temp",
    "processed": "_processed"
}

# Plot styling parameters
PLOT_STYLE = {
    "figure_size": (12, 8),
    "dpi": 300,
    "font_size": 12,
    "title_font_size": 14,
    "label_font_size": 10,
    "legend_font_size": 10,
    "line_width": 2,
    "marker_size": 6,
    "alpha_fill": 0.3,
    "alpha_line": 0.8
}

# Data file patterns
DATA_FILE_PATTERNS = {
    "epidemiological": "*epi_timeseries*.csv",
    "demographic": "*births*.csv",
    "vaccination": "*mcv*.csv",
    "population": "*population*.csv",
    "age_distribution": "*age*.csv"
}

# Confidence intervals
CONFIDENCE_LEVELS = [0.5, 0.95]  # 50% and 95% confidence intervals

# Logging configuration
LOG_CONFIG = {
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "date_format": "%Y-%m-%d %H:%M:%S",
    "level": "INFO"
}

# Error messages
ERROR_MESSAGES = {
    "file_not_found": "Required data file not found: {filename}",
    "invalid_state": "Invalid state name: {state}. Must be one of: {valid_states}",
    "invalid_year": "Invalid year: {year}. Must be between {min_year} and {max_year}",
    "missing_data": "Missing required data columns: {columns}",
    "convergence_failed": "Model optimization failed to converge",
    "invalid_parameters": "Invalid model parameters: {details}"
}

# Success messages  
SUCCESS_MESSAGES = {
    "model_fitted": "Model fitted successfully for state: {state}",
    "data_loaded": "Data loaded successfully: {filename}",
    "plot_saved": "Plot saved successfully: {filename}",
    "analysis_complete": "Analysis completed for {n_states} states"
}

# Default configuration values
DEFAULT_CONFIG = {
    "random_seed": 42,
    "n_bootstrap_samples": 1000,
    "parallel_processes": 4,
    "memory_limit_gb": 8,
    "cache_enabled": True
}


def get_state_color(state: str) -> str:
    """
    Get a consistent color for a state.
    
    Args:
        state: State name
        
    Returns:
        Hex color code
    """
    if state not in STATES_SOUTHERN_NIGERIA:
        raise ValueError(f"Unknown state: {state}")
    
    state_index = STATES_SOUTHERN_NIGERIA.index(state)
    color_index = state_index % len(EXTENDED_COLORS)
    return EXTENDED_COLORS[color_index]


def get_zone_states(zone: str) -> List[str]:
    """
    Get list of states in a geopolitical zone.
    
    Args:
        zone: Geopolitical zone name
        
    Returns:
        List of state names
    """
    if zone not in GEOPOLITICAL_ZONES:
        raise ValueError(f"Unknown zone: {zone}")
    
    return GEOPOLITICAL_ZONES[zone]


def validate_state(state: str) -> str:
    """
    Validate and normalize state name.
    
    Args:
        state: State name to validate
        
    Returns:
        Normalized state name
        
    Raises:
        ValueError: If state is invalid
    """
    state_lower = state.lower().replace(" ", "_")
    
    if state_lower not in STATES_SOUTHERN_NIGERIA:
        raise ValueError(
            ERROR_MESSAGES["invalid_state"].format(
                state=state, 
                valid_states=", ".join(STATES_SOUTHERN_NIGERIA)
            )
        )
    
    return state_lower


def validate_year(year: int) -> int:
    """
    Validate analysis year.
    
    Args:
        year: Year to validate
        
    Returns:
        Validated year
        
    Raises:
        ValueError: If year is invalid
    """
    min_year = TIME_PARAMETERS["analysis_start_year"]
    max_year = TIME_PARAMETERS["analysis_end_year"]
    
    if not min_year <= year <= max_year:
        raise ValueError(
            ERROR_MESSAGES["invalid_year"].format(
                year=year,
                min_year=min_year,
                max_year=max_year
            )
        )
    
    return year
