"""
Mathematical utilities for the intensification package.

This module provides mathematical and statistical utility functions
used throughout the transmission modeling analysis.
"""

import numpy as np
import pandas as pd
from typing import <PERSON><PERSON>, Union, Optional
from scipy import stats
from sklearn.metrics import r2_score


def low_mid_high(samples: np.ndarray, 
                confidence_levels: list = [0.5, 0.95]) -> Tuple[np.n<PERSON><PERSON>, ...]:
    """
    Compute confidence intervals from sample arrays.
    
    This function computes multiple confidence intervals from posterior samples,
    following the pattern used in the original research code.
    
    Args:
        samples: Array of samples with shape (n_samples, n_timepoints)
        confidence_levels: List of confidence levels (e.g., [0.5, 0.95])
        
    Returns:
        Tuple of arrays: (low_95, low_50, median, high_50, high_95)
        For custom confidence levels, returns corresponding bounds
    """
    if samples.ndim == 1:
        samples = samples.reshape(1, -1)
    
    # Sort confidence levels to ensure proper ordering
    confidence_levels = sorted(confidence_levels)
    
    results = []
    
    # Compute bounds for each confidence level
    for level in confidence_levels:
        alpha = 1 - level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        lower_bound = np.percentile(samples, lower_percentile, axis=0)
        upper_bound = np.percentile(samples, upper_percentile, axis=0)
        
        results.extend([lower_bound, upper_bound])
    
    # Add median
    median = np.percentile(samples, 50.0, axis=0)
    
    # Reorganize results: low bounds, median, high bounds
    n_levels = len(confidence_levels)
    low_bounds = [results[i*2] for i in range(n_levels)]
    high_bounds = [results[i*2 + 1] for i in range(n_levels)]
    
    # Return in order: outermost low, inner low, median, inner high, outermost high
    if n_levels == 1:
        return low_bounds[0], median, high_bounds[0]
    elif n_levels == 2:
        return low_bounds[1], low_bounds[0], median, high_bounds[0], high_bounds[1]
    else:
        return tuple(low_bounds[::-1] + [median] + high_bounds)


def compute_confidence_intervals(data: np.ndarray, 
                               confidence_level: float = 0.95) -> Tuple[np.ndarray, np.ndarray]:
    """
    Compute confidence intervals for data.
    
    Args:
        data: Input data array
        confidence_level: Confidence level (between 0 and 1)
        
    Returns:
        Tuple of (lower_bound, upper_bound) arrays
    """
    if not 0 < confidence_level < 1:
        raise ValueError("Confidence level must be between 0 and 1")
    
    alpha = 1 - confidence_level
    lower_percentile = (alpha / 2) * 100
    upper_percentile = (1 - alpha / 2) * 100
    
    lower_bound = np.percentile(data, lower_percentile, axis=0)
    upper_bound = np.percentile(data, upper_percentile, axis=0)
    
    return lower_bound, upper_bound


def fit_quality_metrics(observed: np.ndarray, predicted: np.ndarray, 
                       prediction_intervals: Optional[Tuple[np.ndarray, np.ndarray]] = None,
                       verbose: bool = True) -> dict:
    """
    Compute model fit quality metrics.
    
    Args:
        observed: Observed data values
        predicted: Predicted values (median/mean)
        prediction_intervals: Optional tuple of (lower, upper) prediction bounds
        verbose: Whether to print results
        
    Returns:
        Dictionary of fit quality metrics
    """
    # R-squared score
    r2 = r2_score(observed, predicted)
    
    # Mean absolute error
    mae = np.mean(np.abs(observed - predicted))
    
    # Root mean squared error
    rmse = np.sqrt(np.mean((observed - predicted) ** 2))
    
    # Mean absolute percentage error (avoid division by zero)
    mape = np.mean(np.abs((observed - predicted) / (observed + 1e-10))) * 100
    
    metrics = {
        'r2_score': r2,
        'mae': mae,
        'rmse': rmse,
        'mape': mape
    }
    
    # Coverage metrics if prediction intervals provided
    if prediction_intervals is not None:
        lower, upper = prediction_intervals
        
        # Check which observations fall within intervals
        within_interval = (observed >= lower) & (observed <= upper)
        coverage = np.mean(within_interval)
        
        metrics['interval_coverage'] = coverage
        metrics['n_within_interval'] = np.sum(within_interval)
        metrics['n_total'] = len(observed)
    
    if verbose:
        print("Model Fit Quality Metrics:")
        print(f"R² score: {r2:.4f}")
        print(f"MAE: {mae:.4f}")
        print(f"RMSE: {rmse:.4f}")
        print(f"MAPE: {mape:.2f}%")
        
        if prediction_intervals is not None:
            print(f"Interval coverage: {coverage:.2%}")
            print(f"Points within interval: {np.sum(within_interval)}/{len(observed)}")
    
    return metrics


def smooth_time_series(data: np.ndarray, window_size: int = 3, 
                      method: str = 'moving_average') -> np.ndarray:
    """
    Smooth a time series using various methods.
    
    Args:
        data: Input time series data
        window_size: Size of smoothing window
        method: Smoothing method ('moving_average', 'exponential', 'savgol')
        
    Returns:
        Smoothed time series
    """
    if method == 'moving_average':
        # Simple moving average
        smoothed = np.convolve(data, np.ones(window_size)/window_size, mode='same')
        
    elif method == 'exponential':
        # Exponential smoothing
        alpha = 2.0 / (window_size + 1)
        smoothed = np.zeros_like(data)
        smoothed[0] = data[0]
        
        for i in range(1, len(data)):
            smoothed[i] = alpha * data[i] + (1 - alpha) * smoothed[i-1]
            
    elif method == 'savgol':
        # Savitzky-Golay filter
        from scipy.signal import savgol_filter
        smoothed = savgol_filter(data, window_size, 2)
        
    else:
        raise ValueError(f"Unknown smoothing method: {method}")
    
    return smoothed


def normalize_array(arr: np.ndarray, method: str = 'minmax') -> np.ndarray:
    """
    Normalize an array using various methods.
    
    Args:
        arr: Input array
        method: Normalization method ('minmax', 'zscore', 'robust')
        
    Returns:
        Normalized array
    """
    if method == 'minmax':
        # Min-max normalization to [0, 1]
        arr_min, arr_max = np.min(arr), np.max(arr)
        if arr_max == arr_min:
            return np.zeros_like(arr)
        return (arr - arr_min) / (arr_max - arr_min)
        
    elif method == 'zscore':
        # Z-score normalization
        return (arr - np.mean(arr)) / np.std(arr)
        
    elif method == 'robust':
        # Robust normalization using median and MAD
        median = np.median(arr)
        mad = np.median(np.abs(arr - median))
        if mad == 0:
            return np.zeros_like(arr)
        return (arr - median) / mad
        
    else:
        raise ValueError(f"Unknown normalization method: {method}")


def compute_autocorrelation(data: np.ndarray, max_lag: Optional[int] = None) -> np.ndarray:
    """
    Compute autocorrelation function of a time series.
    
    Args:
        data: Input time series
        max_lag: Maximum lag to compute (default: len(data)//4)
        
    Returns:
        Autocorrelation values
    """
    if max_lag is None:
        max_lag = len(data) // 4
    
    # Center the data
    data_centered = data - np.mean(data)
    
    # Compute autocorrelation using numpy correlate
    autocorr = np.correlate(data_centered, data_centered, mode='full')
    
    # Take the second half (positive lags) and normalize
    mid = len(autocorr) // 2
    autocorr = autocorr[mid:mid + max_lag + 1]
    autocorr = autocorr / autocorr[0]  # Normalize by lag-0 value
    
    return autocorr


def bootstrap_confidence_interval(data: np.ndarray, statistic_func: callable,
                                n_bootstrap: int = 1000, 
                                confidence_level: float = 0.95,
                                random_seed: Optional[int] = None) -> Tuple[float, float]:
    """
    Compute bootstrap confidence interval for a statistic.
    
    Args:
        data: Input data
        statistic_func: Function to compute statistic (e.g., np.mean)
        n_bootstrap: Number of bootstrap samples
        confidence_level: Confidence level
        random_seed: Random seed for reproducibility
        
    Returns:
        Tuple of (lower_bound, upper_bound)
    """
    if random_seed is not None:
        np.random.seed(random_seed)
    
    bootstrap_stats = []
    
    for _ in range(n_bootstrap):
        # Resample with replacement
        bootstrap_sample = np.random.choice(data, size=len(data), replace=True)
        stat = statistic_func(bootstrap_sample)
        bootstrap_stats.append(stat)
    
    bootstrap_stats = np.array(bootstrap_stats)
    
    # Compute confidence interval
    alpha = 1 - confidence_level
    lower_percentile = (alpha / 2) * 100
    upper_percentile = (1 - alpha / 2) * 100
    
    lower_bound = np.percentile(bootstrap_stats, lower_percentile)
    upper_bound = np.percentile(bootstrap_stats, upper_percentile)
    
    return lower_bound, upper_bound
