"""
File utilities for the intensification package.

This module provides utility functions for file operations, including
directory management, pickle operations, and data file handling.
"""

import pickle
import json
from pathlib import Path
from typing import Any, Dict, Optional, Union
import pandas as pd
import numpy as np
import logging

from .logging_config import get_logger


def ensure_directory(path: Union[str, Path]) -> Path:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        path: Directory path to ensure exists
        
    Returns:
        Path object for the directory
    """
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def save_pickle(obj: Any, filepath: Union[str, Path]) -> None:
    """
    Save an object to a pickle file.
    
    Args:
        obj: Object to save
        filepath: Path to save the pickle file
    """
    filepath = Path(filepath)
    ensure_directory(filepath.parent)
    
    logger = get_logger(__name__)
    logger.debug(f"Saving pickle file: {filepath}")
    
    try:
        with open(filepath, 'wb') as f:
            pickle.dump(obj, f, protocol=pickle.HIGHEST_PROTOCOL)
        logger.debug(f"Successfully saved pickle file: {filepath}")
    except Exception as e:
        logger.error(f"Failed to save pickle file {filepath}: {e}")
        raise


def load_pickle(filepath: Union[str, Path]) -> Any:
    """
    Load an object from a pickle file.
    
    Args:
        filepath: Path to the pickle file
        
    Returns:
        Loaded object
    """
    filepath = Path(filepath)
    
    if not filepath.exists():
        raise FileNotFoundError(f"Pickle file not found: {filepath}")
    
    logger = get_logger(__name__)
    logger.debug(f"Loading pickle file: {filepath}")
    
    try:
        with open(filepath, 'rb') as f:
            obj = pickle.load(f)
        logger.debug(f"Successfully loaded pickle file: {filepath}")
        return obj
    except Exception as e:
        logger.error(f"Failed to load pickle file {filepath}: {e}")
        raise


def save_json(data: Dict[str, Any], filepath: Union[str, Path], indent: int = 2) -> None:
    """
    Save data to a JSON file.
    
    Args:
        data: Data to save
        filepath: Path to save the JSON file
        indent: JSON indentation level
    """
    filepath = Path(filepath)
    ensure_directory(filepath.parent)
    
    logger = get_logger(__name__)
    logger.debug(f"Saving JSON file: {filepath}")
    
    # Convert numpy types to native Python types for JSON serialization
    def convert_numpy(obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: convert_numpy(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    converted_data = convert_numpy(data)
    
    try:
        with open(filepath, 'w') as f:
            json.dump(converted_data, f, indent=indent)
        logger.debug(f"Successfully saved JSON file: {filepath}")
    except Exception as e:
        logger.error(f"Failed to save JSON file {filepath}: {e}")
        raise


def load_json(filepath: Union[str, Path]) -> Dict[str, Any]:
    """
    Load data from a JSON file.
    
    Args:
        filepath: Path to the JSON file
        
    Returns:
        Loaded data
    """
    filepath = Path(filepath)
    
    if not filepath.exists():
        raise FileNotFoundError(f"JSON file not found: {filepath}")
    
    logger = get_logger(__name__)
    logger.debug(f"Loading JSON file: {filepath}")
    
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        logger.debug(f"Successfully loaded JSON file: {filepath}")
        return data
    except Exception as e:
        logger.error(f"Failed to load JSON file {filepath}: {e}")
        raise


def save_dataframe(df: pd.DataFrame, filepath: Union[str, Path], 
                  format: str = 'csv', **kwargs) -> None:
    """
    Save a pandas DataFrame to file.
    
    Args:
        df: DataFrame to save
        filepath: Path to save the file
        format: File format ('csv', 'parquet', 'excel')
        **kwargs: Additional arguments for the save function
    """
    filepath = Path(filepath)
    ensure_directory(filepath.parent)
    
    logger = get_logger(__name__)
    logger.debug(f"Saving DataFrame to {format} file: {filepath}")
    
    try:
        if format.lower() == 'csv':
            df.to_csv(filepath, **kwargs)
        elif format.lower() == 'parquet':
            df.to_parquet(filepath, **kwargs)
        elif format.lower() == 'excel':
            df.to_excel(filepath, **kwargs)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        logger.debug(f"Successfully saved DataFrame: {filepath}")
    except Exception as e:
        logger.error(f"Failed to save DataFrame {filepath}: {e}")
        raise


def load_dataframe(filepath: Union[str, Path], format: Optional[str] = None, 
                  **kwargs) -> pd.DataFrame:
    """
    Load a pandas DataFrame from file.
    
    Args:
        filepath: Path to the file
        format: File format (auto-detected if None)
        **kwargs: Additional arguments for the load function
        
    Returns:
        Loaded DataFrame
    """
    filepath = Path(filepath)
    
    if not filepath.exists():
        raise FileNotFoundError(f"File not found: {filepath}")
    
    # Auto-detect format if not specified
    if format is None:
        format = filepath.suffix.lower().lstrip('.')
    
    logger = get_logger(__name__)
    logger.debug(f"Loading DataFrame from {format} file: {filepath}")
    
    try:
        if format == 'csv':
            df = pd.read_csv(filepath, **kwargs)
        elif format == 'parquet':
            df = pd.read_parquet(filepath, **kwargs)
        elif format in ['xlsx', 'xls', 'excel']:
            df = pd.read_excel(filepath, **kwargs)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        logger.debug(f"Successfully loaded DataFrame: {filepath} (shape: {df.shape})")
        return df
    except Exception as e:
        logger.error(f"Failed to load DataFrame {filepath}: {e}")
        raise


def get_file_size(filepath: Union[str, Path]) -> int:
    """
    Get file size in bytes.
    
    Args:
        filepath: Path to the file
        
    Returns:
        File size in bytes
    """
    filepath = Path(filepath)
    if not filepath.exists():
        raise FileNotFoundError(f"File not found: {filepath}")
    
    return filepath.stat().st_size


def list_files(directory: Union[str, Path], pattern: str = "*", 
              recursive: bool = False) -> list[Path]:
    """
    List files in a directory matching a pattern.
    
    Args:
        directory: Directory to search
        pattern: File pattern to match
        recursive: Whether to search recursively
        
    Returns:
        List of matching file paths
    """
    directory = Path(directory)
    
    if not directory.exists():
        raise FileNotFoundError(f"Directory not found: {directory}")
    
    if recursive:
        files = list(directory.rglob(pattern))
    else:
        files = list(directory.glob(pattern))
    
    # Filter to only files (not directories)
    files = [f for f in files if f.is_file()]
    
    return sorted(files)


def backup_file(filepath: Union[str, Path], backup_suffix: str = ".bak") -> Path:
    """
    Create a backup copy of a file.
    
    Args:
        filepath: Path to the file to backup
        backup_suffix: Suffix to add to backup filename
        
    Returns:
        Path to the backup file
    """
    filepath = Path(filepath)
    
    if not filepath.exists():
        raise FileNotFoundError(f"File not found: {filepath}")
    
    backup_path = filepath.with_suffix(filepath.suffix + backup_suffix)
    
    logger = get_logger(__name__)
    logger.debug(f"Creating backup: {filepath} -> {backup_path}")
    
    try:
        import shutil
        shutil.copy2(filepath, backup_path)
        logger.debug(f"Successfully created backup: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Failed to create backup {backup_path}: {e}")
        raise
