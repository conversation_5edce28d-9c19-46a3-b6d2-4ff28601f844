"""
Models module for the intensification package.

This module contains specialized model implementations for different aspects
of measles transmission analysis, including age-structured models, 
demographic models, and epidemiological curve fitting.
"""

from .age_infection import AgeAtInfectionModel
from .birth_seasonality import BirthSeasonalityModel
from .coverage_estimation import CoverageEstimationModel
from .case_classification import CaseClassificationModel

__all__ = [
    "AgeAtInfectionModel",
    "BirthSeasonalityModel",
    "CoverageEstimationModel",
    "CaseClassificationModel"
]
