"""
Data loading utilities for the intensification package.

This module provides centralized data loading functionality for all types
of data used in the measles transmission analysis.
"""

from typing import Dict, List, Optional, Union
from pathlib import Path
import pandas as pd
import numpy as np

from ..core.config import get_config
from ..utils import get_logger, load_dataframe
from ..utils.constants import STATES_SOUTHERN_NIGERIA, validate_state


class DataLoader:
    """
    Centralized data loader for all epidemiological and demographic data.
    
    This class provides methods to load and validate all data types used
    in the transmission modeling analysis.
    """
    
    def __init__(self, data_dir: Optional[Path] = None):
        """
        Initialize the data loader.
        
        Args:
            data_dir: Optional custom data directory path
        """
        self.logger = get_logger(__name__)
        self.config = get_config()
        
        if data_dir is not None:
            self.data_dir = Path(data_dir)
        else:
            self.data_dir = self.config.data_paths.data_dir
        
        self.logger.info(f"DataLoader initialized with data directory: {self.data_dir}")
    
    def load_states_and_regions(self) -> pd.DataFrame:
        """
        Load states and regions mapping data.
        
        Returns:
            DataFrame with state and region information
        """
        filepath = self.data_dir / self.config.data_paths.states_regions
        self.logger.debug(f"Loading states and regions from: {filepath}")
        
        df = load_dataframe(filepath, index_col=0)
        
        # Validate required columns
        required_cols = ['state', 'region']
        missing_cols = set(required_cols) - set(df.columns)
        if missing_cols:
            raise ValueError(f"Missing required columns in states data: {missing_cols}")
        
        self.logger.info(f"Loaded {len(df)} states and regions")
        return df
    
    def load_epidemiological_data(self, state: Optional[str] = None) -> pd.DataFrame:
        """
        Load epidemiological time series data.
        
        Args:
            state: Optional state name. If None, loads all states.
            
        Returns:
            DataFrame with epidemiological time series
        """
        filepath = self.data_dir / self.config.data_paths.epi_timeseries
        self.logger.debug(f"Loading epidemiological data from: {filepath}")
        
        df = load_dataframe(filepath)
        
        # Validate required columns
        required_cols = ['state', 'date', 'cases']
        missing_cols = set(required_cols) - set(df.columns)
        if missing_cols:
            raise ValueError(f"Missing required columns in epi data: {missing_cols}")
        
        # Filter by state if specified
        if state is not None:
            state = validate_state(state)
            df = df[df['state'] == state].copy()
            
            if df.empty:
                raise ValueError(f"No epidemiological data found for state: {state}")
        
        # Convert date column
        df['date'] = pd.to_datetime(df['date'])
        
        self.logger.info(f"Loaded epidemiological data: {len(df)} records")
        return df
    
    def load_demographic_data(self, state: Optional[str] = None) -> pd.DataFrame:
        """
        Load demographic data (births, population).
        
        Args:
            state: Optional state name. If None, loads all states.
            
        Returns:
            DataFrame with demographic data
        """
        # Load monthly births data
        births_path = self.data_dir / self.config.data_paths.monthly_births
        self.logger.debug(f"Loading births data from: {births_path}")
        
        births_df = load_dataframe(births_path)
        
        # Load population data
        pop_path = self.data_dir / self.config.data_paths.population_data
        self.logger.debug(f"Loading population data from: {pop_path}")
        
        pop_df = load_dataframe(pop_path)
        
        # Merge demographic data
        demo_df = pd.merge(births_df, pop_df, on='state', how='outer')
        
        # Filter by state if specified
        if state is not None:
            state = validate_state(state)
            demo_df = demo_df[demo_df['state'] == state].copy()
            
            if demo_df.empty:
                raise ValueError(f"No demographic data found for state: {state}")
        
        self.logger.info(f"Loaded demographic data: {len(demo_df)} records")
        return demo_df
    
    def load_vaccination_data(self, state: Optional[str] = None) -> pd.DataFrame:
        """
        Load vaccination coverage and campaign data.
        
        Args:
            state: Optional state name. If None, loads all states.
            
        Returns:
            DataFrame with vaccination data
        """
        # Load MCV1 coverage data
        mcv1_path = self.data_dir / self.config.data_paths.mcv1_stats
        self.logger.debug(f"Loading MCV1 data from: {mcv1_path}")
        
        mcv1_df = load_dataframe(mcv1_path)
        
        # Load SIA calendar data
        sia_path = self.data_dir / self.config.data_paths.sia_calendar
        self.logger.debug(f"Loading SIA calendar from: {sia_path}")
        
        sia_df = load_dataframe(sia_path)
        
        # Merge vaccination data
        vacc_df = pd.merge(mcv1_df, sia_df, on='state', how='outer')
        
        # Filter by state if specified
        if state is not None:
            state = validate_state(state)
            vacc_df = vacc_df[vacc_df['state'] == state].copy()
            
            if vacc_df.empty:
                raise ValueError(f"No vaccination data found for state: {state}")
        
        self.logger.info(f"Loaded vaccination data: {len(vacc_df)} records")
        return vacc_df
    
    def load_age_distribution_data(self) -> pd.DataFrame:
        """
        Load age distribution data.
        
        Returns:
            DataFrame with age distribution information
        """
        filepath = self.data_dir / self.config.data_paths.age_distributions
        self.logger.debug(f"Loading age distribution data from: {filepath}")
        
        df = load_dataframe(filepath)
        
        self.logger.info(f"Loaded age distribution data: {len(df)} records")
        return df
    
    def load_birth_seasonality_data(self) -> pd.DataFrame:
        """
        Load birth seasonality profiles.
        
        Returns:
            DataFrame with birth seasonality data
        """
        filepath = self.data_dir / self.config.data_paths.birth_seasonality
        self.logger.debug(f"Loading birth seasonality data from: {filepath}")
        
        df = load_dataframe(filepath)
        
        self.logger.info(f"Loaded birth seasonality data: {len(df)} records")
        return df
    
    def load_age_at_infection_data(self) -> pd.DataFrame:
        """
        Load age at infection distribution data.
        
        Returns:
            DataFrame with age at infection data
        """
        filepath = self.data_dir / self.config.data_paths.age_infection
        self.logger.debug(f"Loading age at infection data from: {filepath}")
        
        df = load_dataframe(filepath)
        
        self.logger.info(f"Loaded age at infection data: {len(df)} records")
        return df
    
    def load_all_state_data(self, state: str) -> Dict[str, pd.DataFrame]:
        """
        Load all data for a specific state.
        
        Args:
            state: State name
            
        Returns:
            Dictionary containing all data types for the state
        """
        state = validate_state(state)
        self.logger.info(f"Loading all data for state: {state}")
        
        data = {
            'epidemiological': self.load_epidemiological_data(state),
            'demographic': self.load_demographic_data(state),
            'vaccination': self.load_vaccination_data(state),
            'age_distribution': self.load_age_distribution_data(),
            'birth_seasonality': self.load_birth_seasonality_data(),
            'age_at_infection': self.load_age_at_infection_data()
        }
        
        self.logger.info(f"Successfully loaded all data for state: {state}")
        return data
    
    def validate_data_availability(self, states: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        Validate data availability for specified states.
        
        Args:
            states: List of states to check. If None, checks all southern states.
            
        Returns:
            Dictionary mapping state names to availability status
        """
        if states is None:
            states = STATES_SOUTHERN_NIGERIA
        
        availability = {}
        
        for state in states:
            try:
                state = validate_state(state)
                self.load_all_state_data(state)
                availability[state] = True
                self.logger.debug(f"Data available for state: {state}")
            except Exception as e:
                availability[state] = False
                self.logger.warning(f"Data not available for state {state}: {e}")
        
        available_count = sum(availability.values())
        total_count = len(availability)
        
        self.logger.info(f"Data availability: {available_count}/{total_count} states")
        
        return availability
    
    def get_data_summary(self) -> Dict[str, Dict[str, int]]:
        """
        Get summary statistics for all available data.
        
        Returns:
            Dictionary with data summary statistics
        """
        summary = {}
        
        try:
            # States and regions
            states_df = self.load_states_and_regions()
            summary['states_regions'] = {
                'total_records': len(states_df),
                'unique_states': states_df['state'].nunique(),
                'unique_regions': states_df['region'].nunique()
            }
        except Exception as e:
            self.logger.warning(f"Could not load states data: {e}")
            summary['states_regions'] = {'error': str(e)}
        
        # Add summaries for other data types
        data_types = [
            ('epidemiological', self.load_epidemiological_data),
            ('demographic', self.load_demographic_data),
            ('vaccination', self.load_vaccination_data),
            ('age_distribution', self.load_age_distribution_data),
            ('birth_seasonality', self.load_birth_seasonality_data),
            ('age_at_infection', self.load_age_at_infection_data)
        ]
        
        for data_type, load_func in data_types:
            try:
                df = load_func()
                summary[data_type] = {
                    'total_records': len(df),
                    'columns': list(df.columns),
                    'date_range': self._get_date_range(df) if 'date' in df.columns else None
                }
            except Exception as e:
                self.logger.warning(f"Could not load {data_type} data: {e}")
                summary[data_type] = {'error': str(e)}
        
        return summary
    
    def _get_date_range(self, df: pd.DataFrame) -> Dict[str, str]:
        """Get date range from DataFrame with date column."""
        if 'date' in df.columns:
            dates = pd.to_datetime(df['date'])
            return {
                'start': dates.min().strftime('%Y-%m-%d'),
                'end': dates.max().strftime('%Y-%m-%d')
            }
        return None
