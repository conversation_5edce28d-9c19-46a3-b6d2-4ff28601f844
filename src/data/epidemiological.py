"""
Epidemiological data processing for the intensification package.

This module handles processing of case-based surveillance data, including
case classification, time series aggregation, and data validation.
"""

from typing import Dict, List, Optional, Tuple, Union
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.linear_model import LogisticRegression

from ..utils import get_logger
from ..utils.constants import validate_state


class EpidemiologicalDataProcessor:
    """
    Processor for epidemiological surveillance data.
    
    This class handles the processing of case-based surveillance data,
    including classification of untested cases and time series aggregation.
    """
    
    def __init__(self):
        """Initialize the epidemiological data processor."""
        self.logger = get_logger(__name__)
        self._case_classifier: Optional[LogisticRegression] = None
    
    def process_surveillance_data(self, raw_data: pd.DataFrame, 
                                state: str) -> pd.DataFrame:
        """
        Process raw surveillance data for a state.
        
        Args:
            raw_data: Raw surveillance data
            state: State name
            
        Returns:
            Processed epidemiological time series
        """
        state = validate_state(state)
        self.logger.info(f"Processing surveillance data for {state}")
        
        # Validate input data
        self._validate_surveillance_data(raw_data)
        
        # Filter data for the state
        state_data = raw_data[raw_data['state'] == state].copy()
        
        if state_data.empty:
            raise ValueError(f"No surveillance data found for state: {state}")
        
        # Process case classifications
        processed_data = self._classify_cases(state_data)
        
        # Aggregate to time series
        time_series = self._aggregate_time_series(processed_data)
        
        # Add derived variables
        time_series = self._add_derived_variables(time_series)
        
        self.logger.info(f"Processed {len(time_series)} time points for {state}")
        return time_series
    
    def _validate_surveillance_data(self, data: pd.DataFrame) -> None:
        """Validate surveillance data format and content."""
        required_columns = [
            'state', 'date', 'age', 'case_status', 'vaccine_doses', 
            'lab_result', 'symptoms'
        ]
        
        missing_cols = set(required_columns) - set(data.columns)
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Check for valid case statuses
        valid_statuses = ['confirmed', 'rejected', 'untested', 'epidemiologically_linked']
        invalid_statuses = set(data['case_status']) - set(valid_statuses)
        if invalid_statuses:
            self.logger.warning(f"Unknown case statuses found: {invalid_statuses}")
        
        # Check date format
        try:
            pd.to_datetime(data['date'])
        except Exception as e:
            raise ValueError(f"Invalid date format in surveillance data: {e}")
    
    def _classify_cases(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Classify untested cases using logistic regression.
        
        This implements the approach described in Appendix 1 of the research paper.
        """
        self.logger.debug("Classifying untested cases")
        
        # Prepare data for classification
        data = data.copy()
        data['date'] = pd.to_datetime(data['date'])
        
        # Create features for classification
        data = self._create_classification_features(data)
        
        # Train classifier on tested cases
        self._train_case_classifier(data)
        
        # Classify untested cases
        data = self._apply_case_classification(data)
        
        return data
    
    def _create_classification_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create features for case classification model."""
        data = data.copy()
        
        # Age features
        data['age_over_5'] = (data['age'] > 5).astype(int)
        
        # Vaccination features
        data['dose_1'] = (data['vaccine_doses'] == 1).astype(int)
        data['dose_2_plus'] = (data['vaccine_doses'] >= 2).astype(int)
        data['dose_missing'] = data['vaccine_doses'].isna().astype(int)
        
        # Time features (for temporal correlation)
        data['year'] = data['date'].dt.year
        data['month'] = data['date'].dt.month
        data['time_index'] = (data['date'] - data['date'].min()).dt.days // 60  # 2-month periods
        
        return data
    
    def _train_case_classifier(self, data: pd.DataFrame) -> None:
        """Train logistic regression classifier for case confirmation."""
        # Filter to tested cases only
        tested_data = data[data['case_status'].isin(['confirmed', 'rejected'])].copy()
        
        if len(tested_data) < 50:
            self.logger.warning("Limited tested cases for classifier training")
        
        # Create target variable (1 = confirmed, 0 = rejected)
        tested_data['confirmed'] = (tested_data['case_status'] == 'confirmed').astype(int)
        
        # Feature matrix
        feature_cols = ['age_over_5', 'dose_1', 'dose_2_plus', 'dose_missing']
        X = tested_data[feature_cols].values
        y = tested_data['confirmed'].values
        
        # Train classifier
        self._case_classifier = LogisticRegression(random_state=42)
        self._case_classifier.fit(X, y)
        
        # Log classifier performance
        train_score = self._case_classifier.score(X, y)
        self.logger.debug(f"Case classifier training accuracy: {train_score:.3f}")
    
    def _apply_case_classification(self, data: pd.DataFrame) -> pd.DataFrame:
        """Apply trained classifier to untested cases."""
        if self._case_classifier is None:
            raise RuntimeError("Case classifier not trained")
        
        data = data.copy()
        
        # Find untested cases
        untested_mask = data['case_status'] == 'untested'
        untested_data = data[untested_mask]
        
        if len(untested_data) == 0:
            self.logger.debug("No untested cases to classify")
            return data
        
        # Apply classifier
        feature_cols = ['age_over_5', 'dose_1', 'dose_2_plus', 'dose_missing']
        X_untested = untested_data[feature_cols].values
        
        # Get confirmation probabilities
        confirmation_probs = self._case_classifier.predict_proba(X_untested)[:, 1]
        
        # Add temporal correlation (simplified)
        confirmation_probs = self._add_temporal_correlation(
            confirmation_probs, untested_data['time_index'].values
        )
        
        # Store probabilities
        data.loc[untested_mask, 'confirmation_probability'] = confirmation_probs
        
        # Create expected case counts
        data['expected_cases'] = 0.0
        data.loc[data['case_status'] == 'confirmed', 'expected_cases'] = 1.0
        data.loc[data['case_status'] == 'epidemiologically_linked', 'expected_cases'] = 1.0
        data.loc[untested_mask, 'expected_cases'] = confirmation_probs
        
        self.logger.debug(f"Classified {len(untested_data)} untested cases")
        return data
    
    def _add_temporal_correlation(self, probs: np.ndarray, 
                                time_indices: np.ndarray) -> np.ndarray:
        """Add temporal correlation to confirmation probabilities."""
        # Simple smoothing approach - could be enhanced with more sophisticated methods
        if len(probs) < 3:
            return probs
        
        # Sort by time
        sort_idx = np.argsort(time_indices)
        sorted_probs = probs[sort_idx]
        
        # Apply moving average smoothing
        window_size = min(5, len(sorted_probs))
        smoothed_probs = np.convolve(
            sorted_probs, 
            np.ones(window_size) / window_size, 
            mode='same'
        )
        
        # Restore original order
        result = np.zeros_like(probs)
        result[sort_idx] = smoothed_probs
        
        return result
    
    def _aggregate_time_series(self, data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate case data to time series."""
        self.logger.debug("Aggregating to time series")
        
        # Group by date and sum expected cases
        time_series = data.groupby('date').agg({
            'expected_cases': 'sum',
            'case_status': 'count'  # Total reports
        }).reset_index()
        
        time_series.columns = ['date', 'cases', 'total_reports']
        
        # Ensure complete time series (fill missing dates with zeros)
        date_range = pd.date_range(
            start=time_series['date'].min(),
            end=time_series['date'].max(),
            freq='D'
        )
        
        complete_series = pd.DataFrame({'date': date_range})
        time_series = complete_series.merge(time_series, on='date', how='left')
        time_series[['cases', 'total_reports']] = time_series[['cases', 'total_reports']].fillna(0)
        
        return time_series
    
    def _add_derived_variables(self, time_series: pd.DataFrame) -> pd.DataFrame:
        """Add derived variables to time series."""
        time_series = time_series.copy()
        
        # Add time variables
        time_series['year'] = time_series['date'].dt.year
        time_series['month'] = time_series['date'].dt.month
        time_series['week'] = time_series['date'].dt.isocalendar().week
        time_series['day_of_year'] = time_series['date'].dt.dayofyear
        
        # Add cumulative cases
        time_series['cumulative_cases'] = time_series['cases'].cumsum()
        
        # Add moving averages
        time_series['cases_7day_ma'] = time_series['cases'].rolling(
            window=7, center=True
        ).mean()
        
        time_series['cases_30day_ma'] = time_series['cases'].rolling(
            window=30, center=True
        ).mean()
        
        # Add reporting rate (cases per report)
        time_series['reporting_rate'] = time_series['cases'] / (
            time_series['total_reports'] + 1e-10
        )
        
        return time_series
    
    def compute_age_distribution(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Compute age distribution of cases.
        
        Args:
            data: Processed case data
            
        Returns:
            DataFrame with age distribution
        """
        self.logger.debug("Computing age distribution of cases")
        
        # Filter to confirmed/linked cases
        confirmed_data = data[
            data['case_status'].isin(['confirmed', 'epidemiologically_linked'])
        ].copy()
        
        if len(confirmed_data) == 0:
            self.logger.warning("No confirmed cases for age distribution")
            return pd.DataFrame()
        
        # Create age bins
        age_bins = [0, 1, 2, 3, 4, 5, 10, 15, 20, 25, np.inf]
        age_labels = ['<1', '1', '2', '3', '4', '5-9', '10-14', '15-19', '20-24', '25+']
        
        confirmed_data['age_group'] = pd.cut(
            confirmed_data['age'], 
            bins=age_bins, 
            labels=age_labels, 
            right=False
        )
        
        # Compute distribution
        age_dist = confirmed_data.groupby('age_group').agg({
            'expected_cases': 'sum'
        }).reset_index()
        
        age_dist['proportion'] = age_dist['expected_cases'] / age_dist['expected_cases'].sum()
        
        return age_dist
    
    def estimate_reporting_rate(self, time_series: pd.DataFrame, 
                              expected_annual_burden: float) -> Dict[str, float]:
        """
        Estimate surveillance reporting rate.
        
        Args:
            time_series: Processed time series data
            expected_annual_burden: Expected annual case burden
            
        Returns:
            Dictionary with reporting rate estimates
        """
        # Calculate observed annual cases
        annual_cases = time_series.groupby('year')['cases'].sum()
        mean_annual_cases = annual_cases.mean()
        
        # Estimate reporting rate
        reporting_rate = mean_annual_cases / expected_annual_burden
        
        # Calculate confidence interval using Poisson assumption
        total_cases = annual_cases.sum()
        total_years = len(annual_cases)
        
        # Poisson confidence interval
        ci_lower = stats.poisson.ppf(0.025, total_cases) / total_years / expected_annual_burden
        ci_upper = stats.poisson.ppf(0.975, total_cases) / total_years / expected_annual_burden
        
        return {
            'reporting_rate': reporting_rate,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'observed_annual_cases': mean_annual_cases,
            'expected_annual_burden': expected_annual_burden
        }
