"""
Data import wizard for non-programmers.

This module provides user-friendly tools for importing and validating
data from various formats commonly used by public health researchers.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import warnings

from ..utils import get_logger, ensure_directory
from ..utils.constants import validate_state, STATES_SOUTHERN_NIGERIA


class DataImportWizard:
    """
    User-friendly data import wizard for researchers without coding experience.
    
    This class helps researchers import their data from Excel, CSV, or other
    common formats and convert it to the format expected by the package.
    """
    
    def __init__(self, output_dir: Path = Path("_data")):
        """
        Initialize the data import wizard.
        
        Args:
            output_dir: Directory where processed data will be saved
        """
        self.logger = get_logger(__name__)
        self.output_dir = Path(output_dir)
        ensure_directory(self.output_dir)
        
        self.imported_data: Dict[str, pd.DataFrame] = {}
        self.validation_results: Dict[str, List[str]] = {}
        
        self.logger.info("Data Import Wizard initialized")
    
    def import_case_data(self, 
                        file_path: Path, 
                        date_column: str = "date",
                        state_column: str = "state", 
                        age_column: str = "age",
                        status_column: str = "case_status",
                        vaccine_column: str = "vaccine_doses") -> bool:
        """
        Import epidemiological case data from file.
        
        Args:
            file_path: Path to the data file (CSV, Excel, etc.)
            date_column: Name of the date column
            state_column: Name of the state column
            age_column: Name of the age column
            status_column: Name of the case status column
            vaccine_column: Name of the vaccine doses column
            
        Returns:
            True if import successful, False otherwise
        """
        try:
            self.logger.info(f"Importing case data from {file_path}")
            
            # Load data based on file extension
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_path.suffix}")
            
            # Standardize column names
            column_mapping = {
                date_column: 'date',
                state_column: 'state', 
                age_column: 'age',
                status_column: 'case_status',
                vaccine_column: 'vaccine_doses'
            }
            
            df = df.rename(columns=column_mapping)
            
            # Data cleaning and validation
            df = self._clean_case_data(df)
            
            self.imported_data['cases'] = df
            self.logger.info(f"Successfully imported {len(df)} case records")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to import case data: {e}")
            return False
    
    def import_population_data(self,
                             file_path: Path,
                             state_column: str = "state",
                             population_column: str = "population", 
                             birth_rate_column: str = "birth_rate") -> bool:
        """
        Import population and demographic data.
        
        Args:
            file_path: Path to the data file
            state_column: Name of the state column
            population_column: Name of the population column
            birth_rate_column: Name of the birth rate column
            
        Returns:
            True if import successful, False otherwise
        """
        try:
            self.logger.info(f"Importing population data from {file_path}")
            
            # Load data
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                df = pd.read_csv(file_path)
            
            # Standardize columns
            column_mapping = {
                state_column: 'state',
                population_column: 'population',
                birth_rate_column: 'birth_rate'
            }
            
            df = df.rename(columns=column_mapping)
            df = self._clean_population_data(df)
            
            self.imported_data['population'] = df
            self.logger.info(f"Successfully imported population data for {len(df)} states")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to import population data: {e}")
            return False
    
    def import_vaccination_coverage(self,
                                  file_path: Path,
                                  state_column: str = "state",
                                  year_column: str = "year",
                                  coverage_column: str = "mcv1_coverage") -> bool:
        """
        Import routine vaccination coverage data.
        
        Args:
            file_path: Path to the data file
            state_column: Name of the state column
            year_column: Name of the year column
            coverage_column: Name of the coverage column
            
        Returns:
            True if import successful, False otherwise
        """
        try:
            self.logger.info(f"Importing vaccination coverage from {file_path}")
            
            # Load data
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                df = pd.read_csv(file_path)
            
            # Standardize columns
            column_mapping = {
                state_column: 'state',
                year_column: 'year',
                coverage_column: 'mcv1_coverage'
            }
            
            df = df.rename(columns=column_mapping)
            df = self._clean_coverage_data(df)
            
            self.imported_data['coverage'] = df
            self.logger.info(f"Successfully imported coverage data: {len(df)} records")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to import coverage data: {e}")
            return False
    
    def import_campaign_data(self,
                           file_path: Path,
                           state_column: str = "state",
                           date_column: str = "campaign_date",
                           doses_column: str = "doses_delivered") -> bool:
        """
        Import vaccination campaign data.
        
        Args:
            file_path: Path to the data file
            state_column: Name of the state column
            date_column: Name of the campaign date column
            doses_column: Name of the doses delivered column
            
        Returns:
            True if import successful, False otherwise
        """
        try:
            self.logger.info(f"Importing campaign data from {file_path}")
            
            # Load data
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                df = pd.read_csv(file_path)
            
            # Standardize columns
            column_mapping = {
                state_column: 'state',
                date_column: 'campaign_date',
                doses_column: 'doses_delivered'
            }
            
            df = df.rename(columns=column_mapping)
            df = self._clean_campaign_data(df)
            
            self.imported_data['campaigns'] = df
            self.logger.info(f"Successfully imported {len(df)} campaign records")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to import campaign data: {e}")
            return False
    
    def validate_all_data(self) -> Dict[str, List[str]]:
        """
        Validate all imported data and return issues found.
        
        Returns:
            Dictionary mapping data type to list of validation issues
        """
        self.logger.info("Validating all imported data")
        
        validator = DataValidator()
        self.validation_results = {}
        
        for data_type, df in self.imported_data.items():
            issues = validator.validate_dataframe(df, data_type)
            self.validation_results[data_type] = issues
            
            if issues:
                self.logger.warning(f"Validation issues in {data_type}: {len(issues)} issues found")
            else:
                self.logger.info(f"✅ {data_type} data passed validation")
        
        return self.validation_results
    
    def save_processed_data(self) -> bool:
        """
        Save all processed data to standard format files.
        
        Returns:
            True if save successful, False otherwise
        """
        try:
            self.logger.info("Saving processed data files")
            
            # Save each data type to appropriate file
            file_mapping = {
                'cases': 'epidemiological_data.csv',
                'population': 'demographic_data.csv', 
                'coverage': 'vaccination_coverage.csv',
                'campaigns': 'campaign_data.csv'
            }
            
            for data_type, filename in file_mapping.items():
                if data_type in self.imported_data:
                    output_path = self.output_dir / filename
                    self.imported_data[data_type].to_csv(output_path, index=False)
                    self.logger.info(f"Saved {data_type} data to {output_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save processed data: {e}")
            return False
    
    def _clean_case_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize case data."""
        # Convert date column
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        
        # Standardize state names
        df['state'] = df['state'].str.lower().str.replace(' ', '_')
        
        # Clean age data
        df['age'] = pd.to_numeric(df['age'], errors='coerce')
        
        # Standardize case status
        status_mapping = {
            'confirmed': 'confirmed',
            'positive': 'confirmed',
            'rejected': 'rejected', 
            'negative': 'rejected',
            'untested': 'untested',
            'pending': 'untested'
        }
        df['case_status'] = df['case_status'].str.lower().map(status_mapping)
        
        # Clean vaccine doses
        df['vaccine_doses'] = pd.to_numeric(df['vaccine_doses'], errors='coerce')
        
        return df
    
    def _clean_population_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize population data."""
        # Standardize state names
        df['state'] = df['state'].str.lower().str.replace(' ', '_')
        
        # Ensure numeric columns
        df['population'] = pd.to_numeric(df['population'], errors='coerce')
        df['birth_rate'] = pd.to_numeric(df['birth_rate'], errors='coerce')
        
        return df
    
    def _clean_coverage_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize coverage data."""
        # Standardize state names
        df['state'] = df['state'].str.lower().str.replace(' ', '_')
        
        # Ensure numeric columns
        df['year'] = pd.to_numeric(df['year'], errors='coerce')
        df['mcv1_coverage'] = pd.to_numeric(df['mcv1_coverage'], errors='coerce')
        
        # Convert percentages to fractions if needed
        if df['mcv1_coverage'].max() > 1.0:
            df['mcv1_coverage'] = df['mcv1_coverage'] / 100.0
        
        return df
    
    def _clean_campaign_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize campaign data."""
        # Convert date column
        df['campaign_date'] = pd.to_datetime(df['campaign_date'], errors='coerce')
        
        # Standardize state names
        df['state'] = df['state'].str.lower().str.replace(' ', '_')
        
        # Ensure numeric columns
        df['doses_delivered'] = pd.to_numeric(df['doses_delivered'], errors='coerce')
        
        return df


class DataValidator:
    """Validates imported data for common issues."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def validate_dataframe(self, df: pd.DataFrame, data_type: str) -> List[str]:
        """
        Validate a DataFrame and return list of issues.
        
        Args:
            df: DataFrame to validate
            data_type: Type of data ('cases', 'population', 'coverage', 'campaigns')
            
        Returns:
            List of validation issue descriptions
        """
        issues = []
        
        if data_type == 'cases':
            issues.extend(self._validate_case_data(df))
        elif data_type == 'population':
            issues.extend(self._validate_population_data(df))
        elif data_type == 'coverage':
            issues.extend(self._validate_coverage_data(df))
        elif data_type == 'campaigns':
            issues.extend(self._validate_campaign_data(df))
        
        return issues
    
    def _validate_case_data(self, df: pd.DataFrame) -> List[str]:
        """Validate case data."""
        issues = []
        
        # Check required columns
        required_cols = ['date', 'state', 'age', 'case_status']
        missing_cols = set(required_cols) - set(df.columns)
        if missing_cols:
            issues.append(f"Missing required columns: {missing_cols}")
        
        # Check for missing dates
        if df['date'].isna().any():
            issues.append(f"{df['date'].isna().sum()} records have missing dates")
        
        # Check for invalid ages
        if (df['age'] < 0).any() or (df['age'] > 100).any():
            issues.append("Some ages are outside reasonable range (0-100 years)")
        
        # Check state names
        invalid_states = set(df['state'].dropna()) - set(STATES_SOUTHERN_NIGERIA)
        if invalid_states:
            issues.append(f"Unknown state names: {invalid_states}")
        
        return issues
    
    def _validate_population_data(self, df: pd.DataFrame) -> List[str]:
        """Validate population data."""
        issues = []
        
        # Check required columns
        required_cols = ['state', 'population']
        missing_cols = set(required_cols) - set(df.columns)
        if missing_cols:
            issues.append(f"Missing required columns: {missing_cols}")
        
        # Check for reasonable population values
        if (df['population'] < 1000).any() or (df['population'] > 50000000).any():
            issues.append("Some population values seem unreasonable")
        
        return issues
    
    def _validate_coverage_data(self, df: pd.DataFrame) -> List[str]:
        """Validate coverage data."""
        issues = []
        
        # Check coverage values are between 0 and 1
        if (df['mcv1_coverage'] < 0).any() or (df['mcv1_coverage'] > 1).any():
            issues.append("Coverage values should be between 0 and 1")
        
        return issues
    
    def _validate_campaign_data(self, df: pd.DataFrame) -> List[str]:
        """Validate campaign data."""
        issues = []
        
        # Check for missing campaign dates
        if df['campaign_date'].isna().any():
            issues.append("Some campaign records have missing dates")
        
        # Check for reasonable dose numbers
        if (df['doses_delivered'] < 0).any():
            issues.append("Negative dose numbers found")
        
        return issues
