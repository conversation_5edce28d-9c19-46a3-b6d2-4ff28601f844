"""
Neighborhood SIR transmission model implementation.

This module contains the main transmission model class based on the neighborhood SIR
approach described in the research paper. It implements a stochastic transmission model
with spatial correlation and seasonal effects.
"""

from typing import Dict, Any, Optional, Tuple
import numpy as np
import pandas as pd
from scipy.sparse import diags
from scipy.optimize import minimize
import logging

from .base_model import BaseTransmissionModel, ModelParameters, ModelResults, ModelValidator
from ..utils import get_logger


class NeighborhoodSIRModel(BaseTransmissionModel):
    """
    Neighborhood SIR transmission model with spatial correlation.
    
    This model implements a stochastic SIR (Susceptible-Infectious-Recovered) 
    transmission model that incorporates:
    - Spatial neighborhood effects for seasonality correlation
    - Vaccination campaign (SIA) effects
    - Birth and demographic processes
    - Under-reporting of cases
    
    Based on the methodology described in the intensification research paper.
    """
    
    def __init__(self, parameters: Optional[ModelParameters] = None):
        """
        Initialize the Neighborhood SIR model.
        
        Args:
            parameters: Model parameters. If None, uses default parameters.
        """
        super().__init__(parameters)
        self.logger = get_logger(__name__)
        
        # Model-specific attributes
        self._data: Optional[pd.DataFrame] = None
        self._sia_effects: Optional[np.ndarray] = None
        self._design_matrices: Optional[Dict[str, np.ndarray]] = None
        
    def fit(self, 
            data: pd.DataFrame, 
            sia_effects: np.ndarray,
            s0_prior: float,
            s0_var: float,
            **kwargs) -> ModelResults:
        """
        Fit the neighborhood SIR model to epidemiological data.
        
        Args:
            data: DataFrame with columns ['cases', 'adj_births', 'adj_cases_p']
            sia_effects: Array of SIA (vaccination campaign) effects
            s0_prior: Prior estimate of initial susceptible population
            s0_var: Variance of initial susceptible population prior
            **kwargs: Additional fitting parameters
            
        Returns:
            ModelResults object containing fitting results
        """
        self.logger.info("Starting neighborhood SIR model fitting")
        
        # Validate inputs
        self._validate_fit_inputs(data, sia_effects, s0_prior, s0_var)
        
        # Store data and setup model
        self._data = data.copy()
        self._sia_effects = sia_effects
        self._setup_model(s0_prior, s0_var)
        
        # Perform optimization
        results = self._optimize_model(**kwargs)
        
        # Store results and mark as fitted
        self._set_fitted(results)
        self.logger.info("Model fitting completed successfully")
        
        return results
    
    def predict(self, time_points: np.ndarray, **kwargs) -> np.ndarray:
        """
        Generate predictions from the fitted model.
        
        Args:
            time_points: Time points for prediction
            **kwargs: Additional prediction parameters
            
        Returns:
            Array of predicted case counts
        """
        if not self.is_fitted():
            raise RuntimeError("Model must be fitted before making predictions")
        
        # Implementation would generate predictions based on fitted parameters
        # This is a placeholder for the actual prediction logic
        self.logger.info(f"Generating predictions for {len(time_points)} time points")
        
        # For now, return zeros as placeholder
        return np.zeros(len(time_points))
    
    def log_likelihood(self, theta: np.ndarray) -> float:
        """
        Compute the log-likelihood for given parameters.
        
        Args:
            theta: Parameter vector [log_S0, mu_1, mu_2, ..., mu_n]
            
        Returns:
            Log-likelihood value
        """
        if self._data is None:
            raise RuntimeError("Model data not initialized")
        
        # Unpack parameters
        log_s0 = theta[0]
        mu = theta[1:]
        
        # Compute compartment populations
        s_t, i_t, e_t = self._compute_compartments(log_s0, mu)
        
        # Compute transmission rates
        beta_t = self._compute_transmission_rates(s_t, i_t, e_t)
        
        # Compute log-likelihood components
        ll_transmission = self._transmission_likelihood(beta_t, s_t, i_t, e_t)
        ll_s0_prior = self._s0_prior_likelihood(log_s0)
        ll_seasonality = self._seasonality_prior_likelihood(beta_t)
        
        total_ll = ll_transmission + ll_s0_prior + ll_seasonality
        
        return total_ll

    def _validate_fit_inputs(self, data: pd.DataFrame, sia_effects: np.ndarray,
                           s0_prior: float, s0_var: float) -> None:
        """Validate inputs for model fitting."""
        required_columns = ['cases', 'adj_births', 'adj_cases_p']
        ModelValidator.validate_time_series(data, required_columns)

        if sia_effects.ndim != 2:
            raise ValueError("sia_effects must be a 2D array")

        if s0_prior <= 0:
            raise ValueError("s0_prior must be positive")

        if s0_var <= 0:
            raise ValueError("s0_var must be positive")

    def _setup_model(self, s0_prior: float, s0_var: float) -> None:
        """Setup model matrices and priors."""
        if self._data is None:
            raise RuntimeError("Data not initialized")

        # Store model dimensions
        self.T = len(self._data) - 1
        self.num_sias = self._sia_effects.shape[1]

        # Setup S0 prior in log space for numerical stability
        self.log_s0_prior = np.log(s0_prior)
        self.log_s0_prior_var = s0_var / (s0_prior ** 2)

        # Initialize SIA efficacy parameters
        self.mu = self.parameters.mu_guess * np.ones(self.num_sias)
        self.mu_var = np.zeros(self.num_sias)

        # Setup seasonality smoothing matrix
        self._setup_seasonality_matrix()

        # Setup design matrices for linear regression
        self._setup_design_matrices()

    def _setup_seasonality_matrix(self) -> None:
        """Setup the periodic smoothing matrix for seasonality prior."""
        tau = self.parameters.tau
        beta_corr = self.parameters.beta_correlation

        # Create second-order difference matrix with periodic boundary conditions
        D2 = np.diag(tau * [-2]) + np.diag((tau - 1) * [1], k=1) + np.diag((tau - 1) * [1], k=-1)
        D2[0, -1] = 1  # Periodic boundary condition
        D2[-1, 0] = 1

        # Compute penalty matrix
        self.pRW2 = np.dot(D2.T, D2) * ((beta_corr ** 4) / 4.0)

    def _setup_design_matrices(self) -> None:
        """Setup design matrices for the linear portion of transmission regression."""
        if self._data is None:
            raise RuntimeError("Data not initialized")

        # Design matrix for seasonality
        n_periods = len(self.pRW2)
        n_timepoints = len(self._data) - 1
        repeats = int(n_timepoints / n_periods) + 1

        self.X = np.vstack([np.eye(n_periods)] * repeats)[:n_timepoints]

        # Precision matrix and hat matrix
        self.C = np.linalg.inv(np.dot(self.X.T, self.X) + self.pRW2)
        self.H = np.dot(self.X, np.dot(self.C, self.X.T))

    def _compute_compartments(self, log_s0: float, mu: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Compute S, I, E compartment populations."""
        if self._data is None or self._sia_effects is None:
            raise RuntimeError("Model not properly initialized")

        # Adjusted SIA effects
        adj_sias = (mu * self._sia_effects[:-1]).sum(axis=1)

        # Compartment populations
        E_t = self._data["adj_cases_p"].values[1:]  # Exposed (next period infectious)
        I_t = self._data["adj_cases_p"].values[:-1]  # Currently infectious

        # Susceptible population dynamics
        births = self._data["adj_births"].values[:-1]
        S_t = np.exp(log_s0) + np.cumsum(births - E_t - adj_sias)

        return S_t, I_t, E_t

    def _compute_transmission_rates(self, S_t: np.ndarray, I_t: np.ndarray, E_t: np.ndarray) -> np.ndarray:
        """Compute transmission rates beta_t."""
        # Force of infection
        foi = E_t / (S_t * I_t + 1e-10)  # Add small constant to avoid division by zero

        # Apply smoothing via design matrices
        log_foi = np.log(foi + 1e-10)
        beta_t = np.dot(self.H, log_foi)

        return beta_t

    def _transmission_likelihood(self, beta_t: np.ndarray, S_t: np.ndarray,
                               I_t: np.ndarray, E_t: np.ndarray) -> float:
        """Compute transmission component of log-likelihood."""
        # Poisson likelihood for new infections
        expected_infections = S_t * I_t * np.exp(beta_t)
        ll = np.sum(E_t * beta_t + E_t * np.log(S_t * I_t + 1e-10) - expected_infections)
        return ll

    def _s0_prior_likelihood(self, log_s0: float) -> float:
        """Compute S0 prior component of log-likelihood."""
        diff = log_s0 - self.log_s0_prior
        ll = -0.5 * (diff ** 2) / self.log_s0_prior_var
        return ll

    def _seasonality_prior_likelihood(self, beta_t: np.ndarray) -> float:
        """Compute seasonality prior component of log-likelihood."""
        # Penalty for non-smooth seasonality
        penalty = np.dot(beta_t, np.dot(self.pRW2, beta_t))
        ll = -0.5 * penalty
        return ll

    def _optimize_model(self, **kwargs) -> ModelResults:
        """Perform model optimization."""
        # Initial parameter guess
        initial_theta = np.concatenate([
            [self.log_s0_prior],  # log(S0)
            self.mu  # SIA efficacies
        ])

        # Optimization bounds
        bounds = [(-10, 10)]  # log(S0) bounds
        bounds.extend([(0, 1)] * self.num_sias)  # SIA efficacy bounds

        # Objective function (negative log-likelihood)
        def objective(theta):
            try:
                return -self.log_likelihood(theta)
            except Exception as e:
                self.logger.warning(f"Optimization error: {e}")
                return np.inf

        # Perform optimization
        result = minimize(
            objective,
            initial_theta,
            method='L-BFGS-B',
            bounds=bounds,
            options={'maxiter': kwargs.get('maxiter', 1000)}
        )

        if not result.success:
            self.logger.warning(f"Optimization did not converge: {result.message}")

        # Extract fitted parameters
        fitted_params = {
            'log_s0': result.x[0],
            's0': np.exp(result.x[0]),
            'mu': result.x[1:],
            'beta_correlation': self.parameters.beta_correlation,
            'tau': self.parameters.tau
        }

        # Compute compartment data with fitted parameters
        S_t, I_t, E_t = self._compute_compartments(result.x[0], result.x[1:])

        compartment_df = pd.DataFrame({
            'S_t': S_t,
            'I_t': I_t,
            'E_t': E_t,
            'time': range(len(S_t))
        })

        # Create results object
        model_results = ModelResults(
            parameters=fitted_params,
            log_likelihood=-result.fun,
            convergence_info={
                'success': result.success,
                'message': result.message,
                'nit': result.nit,
                'nfev': result.nfev
            },
            compartment_data=compartment_df
        )

        return model_results

    def get_compartment_data(self) -> pd.DataFrame:
        """Get compartment population data from fitted model."""
        if not self.is_fitted():
            raise RuntimeError("Model must be fitted first")

        results = self.get_results()
        if results is None:
            raise RuntimeError("No results available")

        return results.compartment_data
