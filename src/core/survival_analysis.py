"""
Survival analysis for vaccination coverage and immunity estimation.

This module implements survival analysis methods to estimate the fraction of
birth cohorts that remain susceptible across vaccination opportunities, as
described in Appendix 2 of the research paper.
"""

from typing import Dict, List, Tuple, Optional
import numpy as np
import pandas as pd
from dataclasses import dataclass
import logging

from ..utils import get_logger


@dataclass
class VaccinationOpportunity:
    """Represents a vaccination opportunity with timing and coverage."""
    
    name: str
    time_months: float  # Age in months when opportunity occurs
    efficacy: float  # Probability of seroconversion
    coverage_func: callable  # Function to compute coverage for birth cohort
    
    def __post_init__(self):
        """Validate vaccination opportunity parameters."""
        if not 0 <= self.efficacy <= 1:
            raise ValueError("Efficacy must be between 0 and 1")
        if self.time_months < 0:
            raise ValueError("Time must be non-negative")


@dataclass
class CohortAnalysisResult:
    """Results of survival analysis for a birth cohort."""
    
    cohort_year: int
    cohort_size: int
    immunity_fractions: Dict[str, float]  # Fraction immune by source
    infection_fraction: float  # Fraction destined for infection
    age_infection_dist: np.ndarray  # Age distribution of infections
    
    def total_immune_fraction(self) -> float:
        """Calculate total fraction with immunity."""
        return sum(self.immunity_fractions.values())


class SurvivalAnalyzer:
    """
    Survival analysis for estimating vaccination coverage and immunity gaps.
    
    This class implements the survival-based approach described in Appendix 2
    of the research paper to partition birth cohorts by immunity source and
    estimate the fraction left to infection.
    """
    
    def __init__(self):
        """Initialize the survival analyzer."""
        self.logger = get_logger(__name__)
        self._vaccination_opportunities: List[VaccinationOpportunity] = []
        self._age_infection_model: Optional[callable] = None
    
    def add_vaccination_opportunity(self, opportunity: VaccinationOpportunity) -> None:
        """
        Add a vaccination opportunity to the analysis.
        
        Args:
            opportunity: VaccinationOpportunity object
        """
        self._vaccination_opportunities.append(opportunity)
        # Sort by timing
        self._vaccination_opportunities.sort(key=lambda x: x.time_months)
        self.logger.info(f"Added vaccination opportunity: {opportunity.name}")
    
    def set_age_infection_model(self, model_func: callable) -> None:
        """
        Set the age-at-infection distribution model.
        
        Args:
            model_func: Function that takes (age, birth_year) and returns probability
        """
        self._age_infection_model = model_func
        self.logger.info("Age-at-infection model set")
    
    def analyze_cohort(self, birth_year: int, cohort_size: int) -> CohortAnalysisResult:
        """
        Perform survival analysis for a single birth cohort.
        
        Args:
            birth_year: Year of birth for the cohort
            cohort_size: Size of the birth cohort
            
        Returns:
            CohortAnalysisResult with immunity partitioning
        """
        if not self._vaccination_opportunities:
            raise ValueError("No vaccination opportunities defined")
        
        if self._age_infection_model is None:
            raise ValueError("Age-at-infection model not set")
        
        self.logger.info(f"Analyzing cohort born in {birth_year}")
        
        # Find the infection fraction that balances the normalization condition
        infection_fraction = self._solve_infection_fraction(birth_year)
        
        # Compute immunity fractions for each source
        immunity_fractions = self._compute_immunity_fractions(birth_year, infection_fraction)
        
        # Compute age distribution of infections
        age_dist = self._compute_age_infection_distribution(birth_year, infection_fraction)
        
        result = CohortAnalysisResult(
            cohort_year=birth_year,
            cohort_size=cohort_size,
            immunity_fractions=immunity_fractions,
            infection_fraction=infection_fraction,
            age_infection_dist=age_dist
        )
        
        self.logger.info(f"Cohort {birth_year}: {infection_fraction:.3f} fraction to infection")
        return result
    
    def analyze_multiple_cohorts(self, cohort_data: pd.DataFrame) -> List[CohortAnalysisResult]:
        """
        Analyze multiple birth cohorts.
        
        Args:
            cohort_data: DataFrame with columns ['birth_year', 'cohort_size']
            
        Returns:
            List of CohortAnalysisResult objects
        """
        results = []
        
        for _, row in cohort_data.iterrows():
            result = self.analyze_cohort(
                birth_year=int(row['birth_year']),
                cohort_size=int(row['cohort_size'])
            )
            results.append(result)
        
        return results
    
    def _solve_infection_fraction(self, birth_year: int) -> float:
        """
        Solve for the infection fraction that satisfies normalization.
        
        The sum of all immunity source fractions must equal 1.
        """
        # Use bisection method to find the infection fraction
        low, high = 0.0, 1.0
        tolerance = 1e-6
        max_iterations = 100
        
        for _ in range(max_iterations):
            mid = (low + high) / 2
            total_fraction = self._compute_total_fraction(birth_year, mid)
            
            if abs(total_fraction - 1.0) < tolerance:
                return mid
            elif total_fraction < 1.0:
                low = mid
            else:
                high = mid
        
        self.logger.warning(f"Infection fraction solver did not converge for cohort {birth_year}")
        return mid
    
    def _compute_total_fraction(self, birth_year: int, infection_fraction: float) -> float:
        """Compute total fraction across all immunity sources."""
        immunity_fractions = self._compute_immunity_fractions(birth_year, infection_fraction)
        return infection_fraction + sum(immunity_fractions.values())
    
    def _compute_immunity_fractions(self, birth_year: int, infection_fraction: float) -> Dict[str, float]:
        """Compute immunity fractions for each vaccination opportunity."""
        immunity_fractions = {}
        remaining_fraction = 1.0
        
        for opportunity in self._vaccination_opportunities:
            # Coverage for this cohort at this opportunity
            coverage = opportunity.coverage_func(birth_year)
            
            # Survival probability (not infected before this opportunity)
            survival_prob = self._compute_survival_probability(
                birth_year, opportunity.time_months, infection_fraction
            )
            
            # Fraction immunized by this opportunity
            immunized_fraction = (
                opportunity.efficacy * coverage * 
                (remaining_fraction - infection_fraction * (1 - survival_prob))
            )
            
            immunity_fractions[opportunity.name] = immunized_fraction
            remaining_fraction -= immunized_fraction
        
        return immunity_fractions

    def _compute_survival_probability(self, birth_year: int, age_months: float,
                                    infection_fraction: float) -> float:
        """
        Compute probability of surviving (not being infected) until given age.

        Args:
            birth_year: Birth year of cohort
            age_months: Age in months
            infection_fraction: Overall fraction destined for infection

        Returns:
            Survival probability
        """
        if self._age_infection_model is None:
            raise ValueError("Age-at-infection model not set")

        # Integrate age-at-infection distribution up to age_months
        ages = np.linspace(0, age_months, int(age_months) + 1)
        infection_probs = [self._age_infection_model(age, birth_year) for age in ages]

        # Cumulative probability of infection by age_months
        cumulative_infection = np.trapz(infection_probs, ages) * infection_fraction

        # Survival probability
        survival_prob = 1.0 - cumulative_infection

        return max(0.0, min(1.0, survival_prob))  # Clamp to [0, 1]

    def _compute_age_infection_distribution(self, birth_year: int,
                                          infection_fraction: float) -> np.ndarray:
        """
        Compute the age distribution of infections for the cohort.

        Args:
            birth_year: Birth year of cohort
            infection_fraction: Fraction of cohort destined for infection

        Returns:
            Array of infection probabilities by age
        """
        if self._age_infection_model is None:
            raise ValueError("Age-at-infection model not set")

        # Age range (0 to 25 years in months)
        ages = np.arange(0, 25 * 12 + 1)

        # Get infection probabilities by age
        infection_probs = np.array([
            self._age_infection_model(age, birth_year) for age in ages
        ])

        # Normalize to sum to infection_fraction
        total_prob = np.sum(infection_probs)
        if total_prob > 0:
            infection_probs = infection_probs * infection_fraction / total_prob

        return infection_probs

    def estimate_annual_burden(self, cohort_results: List[CohortAnalysisResult]) -> pd.DataFrame:
        """
        Estimate expected annual burden from cohort analysis results.

        Args:
            cohort_results: List of cohort analysis results

        Returns:
            DataFrame with annual burden estimates
        """
        burden_data = []

        for result in cohort_results:
            # Allocate infections across ages/years
            ages_months = np.arange(len(result.age_infection_dist))

            for age_months, infection_prob in enumerate(result.age_infection_dist):
                if infection_prob > 0:
                    infection_year = result.cohort_year + age_months / 12.0
                    expected_cases = result.cohort_size * infection_prob

                    burden_data.append({
                        'year': infection_year,
                        'age_months': age_months,
                        'birth_cohort': result.cohort_year,
                        'expected_cases': expected_cases
                    })

        burden_df = pd.DataFrame(burden_data)

        # Aggregate by year
        annual_burden = burden_df.groupby('year')['expected_cases'].sum().reset_index()
        annual_burden.columns = ['year', 'expected_annual_cases']

        return annual_burden

    def create_default_vaccination_schedule(self) -> None:
        """Create a default vaccination schedule for Nigeria."""
        # MCV1 at 9 months
        mcv1 = VaccinationOpportunity(
            name="MCV1",
            time_months=9.0,
            efficacy=0.825,
            coverage_func=lambda year: 0.7  # Placeholder - should use real data
        )

        # MCV2 at 15 months
        mcv2 = VaccinationOpportunity(
            name="MCV2",
            time_months=15.0,
            efficacy=0.95,
            coverage_func=lambda year: 0.5  # Placeholder - should use real data
        )

        self.add_vaccination_opportunity(mcv1)
        self.add_vaccination_opportunity(mcv2)

        self.logger.info("Default vaccination schedule created")

    def add_campaign_opportunity(self, name: str, campaign_year: int,
                               min_age_months: float, max_age_months: float,
                               efficacy: float = 0.9, coverage: float = 0.9) -> None:
        """
        Add a vaccination campaign opportunity.

        Args:
            name: Campaign name
            campaign_year: Year when campaign occurred
            min_age_months: Minimum age for campaign eligibility
            max_age_months: Maximum age for campaign eligibility
            efficacy: Campaign vaccine efficacy
            coverage: Campaign coverage rate
        """
        def campaign_coverage(birth_year):
            # Check if cohort is eligible for this campaign
            age_at_campaign = (campaign_year - birth_year) * 12
            if min_age_months <= age_at_campaign <= max_age_months:
                return coverage
            else:
                return 0.0

        campaign = VaccinationOpportunity(
            name=name,
            time_months=(min_age_months + max_age_months) / 2,  # Average age
            efficacy=efficacy,
            coverage_func=campaign_coverage
        )

        self.add_vaccination_opportunity(campaign)
        self.logger.info(f"Added campaign: {name} in {campaign_year}")
