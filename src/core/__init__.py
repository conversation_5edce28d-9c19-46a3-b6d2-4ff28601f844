"""
Core module for the intensification package.

This module contains the fundamental classes and functions for measles transmission
modeling, including the main transmission model classes and base interfaces.
"""

from .transmission_model import NeighborhoodSIRModel
from .survival_analysis import SurvivalAnalyzer
from .base_model import BaseTransmissionModel
from .config import Config

__all__ = [
    "NeighborhoodSIRModel",
    "SurvivalAnalyzer", 
    "BaseTransmissionModel",
    "Config"
]
