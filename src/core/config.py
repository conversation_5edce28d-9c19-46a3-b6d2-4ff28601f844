"""
Configuration management for the intensification package.

This module provides configuration classes and utilities for managing
model parameters, data paths, and analysis settings.
"""

from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, field
import json
import yaml
from pydantic import BaseModel, Field, validator


class DataPaths(BaseModel):
    """Configuration for data file paths."""
    
    data_dir: Path = Field(default=Path("_data"), description="Main data directory")
    plots_dir: Path = Field(default=Path("_plots"), description="Plots output directory")
    pickle_dir: Path = Field(default=Path("pickle_jar"), description="Pickle files directory")
    
    # Specific data files
    states_regions: str = "states_and_regions.csv"
    age_distributions: str = "binned_age_distributions.csv"
    birth_seasonality: str = "birth_seasonality_profiles.csv"
    population_data: str = "grid3_population_by_state.csv"
    sia_calendar: str = "imputed_sia_calendar_by_state.csv"
    monthly_births: str = "monthly_births_by_state.csv"
    age_infection: str = "southern_age_at_infection.csv"
    epi_timeseries: str = "southern_states_epi_timeseries.csv"
    mcv1_stats: str = "survey_mcv1_summary_stats.csv"
    
    def get_data_path(self, filename: str) -> Path:
        """Get full path to a data file."""
        return self.data_dir / filename
    
    def get_plot_path(self, filename: str) -> Path:
        """Get full path to a plot file."""
        return self.plots_dir / filename
    
    def get_pickle_path(self, filename: str) -> Path:
        """Get full path to a pickle file."""
        return self.pickle_dir / filename


class ModelConfig(BaseModel):
    """Configuration for transmission model parameters."""
    
    # SIR model parameters
    beta_correlation: float = Field(default=3.0, gt=0, description="Beta correlation parameter")
    tau: int = Field(default=26, gt=0, description="Time periods for seasonality")
    mu_guess: float = Field(default=0.1, ge=0, le=1, description="Initial SIA efficacy guess")
    
    # Vaccination efficacy parameters
    mcv1_efficacy: float = Field(default=0.825, ge=0, le=1, description="MCV1 vaccine efficacy")
    mcv2_efficacy: float = Field(default=0.95, ge=0, le=1, description="MCV2 vaccine efficacy")
    sia_efficacy: float = Field(default=0.9, ge=0, le=1, description="SIA vaccine efficacy")
    
    # Optimization parameters
    max_iterations: int = Field(default=1000, gt=0, description="Maximum optimization iterations")
    convergence_tolerance: float = Field(default=1e-6, gt=0, description="Convergence tolerance")
    
    # Age parameters
    max_age_years: int = Field(default=25, gt=0, description="Maximum age for analysis")
    maternal_protection_months: float = Field(default=6.0, ge=0, description="Maternal protection duration")


class AnalysisConfig(BaseModel):
    """Configuration for analysis settings."""
    
    # States to analyze
    southern_states: List[str] = Field(
        default=[
            "abia", "akwa_ibom", "anambra", "bayelsa", "cross_river", "delta",
            "ebonyi", "edo", "ekiti", "enugu", "imo", "lagos", "ogun", "ondo", 
            "osun", "oyo", "rivers"
        ],
        description="List of southern Nigerian states"
    )
    
    # Default state for examples
    default_state: str = Field(default="lagos", description="Default state for analysis")
    
    # Time periods
    analysis_start_year: int = Field(default=2009, description="Start year for analysis")
    analysis_end_year: int = Field(default=2024, description="End year for analysis")
    forecast_years: int = Field(default=3, gt=0, description="Years for forecasting")
    
    # Confidence intervals
    confidence_levels: List[float] = Field(
        default=[0.5, 0.95], 
        description="Confidence levels for intervals"
    )
    
    # Plotting parameters
    figure_dpi: int = Field(default=300, gt=0, description="Figure DPI for plots")
    figure_format: str = Field(default="png", description="Figure format")
    color_palette: List[str] = Field(
        default=["#375E97", "#FB6542", "#FFBB00", "#5ca904", "#FF6B6B"],
        description="Color palette for plots"
    )


class Config(BaseModel):
    """Main configuration class combining all settings."""
    
    data_paths: DataPaths = Field(default_factory=DataPaths)
    model: ModelConfig = Field(default_factory=ModelConfig)
    analysis: AnalysisConfig = Field(default_factory=AnalysisConfig)
    
    # Logging configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log message format"
    )
    
    # Random seed for reproducibility
    random_seed: Optional[int] = Field(default=42, description="Random seed")
    
    @classmethod
    def from_file(cls, config_path: Path) -> "Config":
        """Load configuration from file."""
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        if config_path.suffix.lower() == '.json':
            with open(config_path, 'r') as f:
                config_data = json.load(f)
        elif config_path.suffix.lower() in ['.yml', '.yaml']:
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")
        
        return cls(**config_data)
    
    def to_file(self, config_path: Path) -> None:
        """Save configuration to file."""
        config_data = self.dict()
        
        # Convert Path objects to strings for serialization
        def convert_paths(obj):
            if isinstance(obj, dict):
                return {k: convert_paths(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_paths(item) for item in obj]
            elif isinstance(obj, Path):
                return str(obj)
            else:
                return obj
        
        config_data = convert_paths(config_data)
        
        if config_path.suffix.lower() == '.json':
            with open(config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
        elif config_path.suffix.lower() in ['.yml', '.yaml']:
            with open(config_path, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False, indent=2)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")
    
    def get_state_data_path(self, state: str, filename: str) -> Path:
        """Get path to state-specific data file."""
        return self.data_paths.get_data_path(f"{state}_{filename}")
    
    def ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        directories = [
            self.data_paths.data_dir,
            self.data_paths.plots_dir,
            self.data_paths.pickle_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)


# Global configuration instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config()
    return _config


def set_config(config: Config) -> None:
    """Set the global configuration instance."""
    global _config
    _config = config


def load_config(config_path: Path) -> Config:
    """Load configuration from file and set as global."""
    config = Config.from_file(config_path)
    set_config(config)
    return config
