"""
Base classes for transmission models in the intensification package.

This module provides abstract base classes and interfaces that define
the common structure for all transmission models in the package.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
import numpy as np
import pandas as pd
from dataclasses import dataclass


@dataclass
class ModelParameters:
    """Container for model parameters with validation."""
    
    beta_correlation: float = 3.0
    tau: int = 26  # Time periods for seasonality
    mu_guess: float = 0.1  # Initial guess for SIA efficacy
    
    def __post_init__(self):
        """Validate parameters after initialization."""
        if self.beta_correlation <= 0:
            raise ValueError("beta_correlation must be positive")
        if self.tau <= 0:
            raise ValueError("tau must be positive")
        if not 0 <= self.mu_guess <= 1:
            raise ValueError("mu_guess must be between 0 and 1")


@dataclass
class ModelResults:
    """Container for model fitting results."""
    
    parameters: Dict[str, Any]
    log_likelihood: float
    convergence_info: Dict[str, Any]
    compartment_data: pd.DataFrame
    posterior_samples: Optional[np.ndarray] = None
    
    def __post_init__(self):
        """Validate results after initialization."""
        if not isinstance(self.compartment_data, pd.DataFrame):
            raise TypeError("compartment_data must be a pandas DataFrame")


class BaseTransmissionModel(ABC):
    """
    Abstract base class for all transmission models.
    
    This class defines the interface that all transmission models must implement,
    providing a consistent API for model fitting, prediction, and evaluation.
    """
    
    def __init__(self, parameters: Optional[ModelParameters] = None):
        """
        Initialize the base transmission model.
        
        Args:
            parameters: Model parameters. If None, uses default parameters.
        """
        self.parameters = parameters or ModelParameters()
        self._fitted = False
        self._results: Optional[ModelResults] = None
    
    @abstractmethod
    def fit(self, data: pd.DataFrame, **kwargs) -> ModelResults:
        """
        Fit the model to data.
        
        Args:
            data: Input data for model fitting
            **kwargs: Additional fitting parameters
            
        Returns:
            ModelResults object containing fitting results
        """
        pass
    
    @abstractmethod
    def predict(self, time_points: np.ndarray, **kwargs) -> np.ndarray:
        """
        Generate predictions from the fitted model.
        
        Args:
            time_points: Time points for prediction
            **kwargs: Additional prediction parameters
            
        Returns:
            Array of predictions
        """
        pass
    
    @abstractmethod
    def log_likelihood(self, theta: np.ndarray) -> float:
        """
        Compute the log-likelihood for given parameters.
        
        Args:
            theta: Parameter vector
            
        Returns:
            Log-likelihood value
        """
        pass
    
    def is_fitted(self) -> bool:
        """Check if the model has been fitted."""
        return self._fitted
    
    def get_results(self) -> Optional[ModelResults]:
        """Get the fitting results."""
        return self._results
    
    def validate_data(self, data: pd.DataFrame) -> None:
        """
        Validate input data format and content.
        
        Args:
            data: Input data to validate
            
        Raises:
            ValueError: If data is invalid
        """
        if not isinstance(data, pd.DataFrame):
            raise TypeError("Data must be a pandas DataFrame")
        
        if data.empty:
            raise ValueError("Data cannot be empty")
    
    def _set_fitted(self, results: ModelResults) -> None:
        """Mark model as fitted and store results."""
        self._fitted = True
        self._results = results


class ModelValidator:
    """Utility class for validating model inputs and outputs."""
    
    @staticmethod
    def validate_time_series(data: pd.DataFrame, required_columns: list) -> None:
        """
        Validate time series data.
        
        Args:
            data: Time series data to validate
            required_columns: List of required column names
            
        Raises:
            ValueError: If validation fails
        """
        missing_cols = set(required_columns) - set(data.columns)
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Check for negative values in case counts
        if 'cases' in data.columns and (data['cases'] < 0).any():
            raise ValueError("Case counts cannot be negative")
    
    @staticmethod
    def validate_parameters(params: Dict[str, Any], param_bounds: Dict[str, Tuple[float, float]]) -> None:
        """
        Validate parameter values against bounds.
        
        Args:
            params: Parameter dictionary
            param_bounds: Dictionary of (min, max) bounds for each parameter
            
        Raises:
            ValueError: If parameters are out of bounds
        """
        for param_name, (min_val, max_val) in param_bounds.items():
            if param_name in params:
                value = params[param_name]
                if not min_val <= value <= max_val:
                    raise ValueError(
                        f"Parameter {param_name} = {value} is outside bounds [{min_val}, {max_val}]"
                    )
