"""
Intensification: Measles Transmission Modeling Package

A modernized Python package for analyzing measles transmission dynamics and vaccination
campaign effectiveness in Southern Nigeria, based on the research paper:
"Routine immunization intensification, vaccination campaigns, and measles transmission 
in Southern Nigeria" (2025).

This package provides tools for:
- Stochastic transmission modeling using neighborhood SIR models
- Survival analysis for vaccination coverage estimation
- Demographic and epidemiological data processing
- Visualization and analysis of vaccination campaign effectiveness

Author: Research Team
Version: 2.0.0 (Modernized)
Python: 3.12+
"""

from .core import *
from .data import *
from .models import *
from .visualization import *
from .utils import *

__version__ = "2.0.0"
__author__ = "Research Team"
__email__ = "<EMAIL>"

# Package metadata
__all__ = [
    # Core modules
    "core",
    "data", 
    "models",
    "visualization",
    "utils",
    # Version info
    "__version__",
    "__author__",
    "__email__"
]
