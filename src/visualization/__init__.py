"""
Visualization module for the intensification package.

This module provides plotting and visualization tools for displaying
transmission model results, epidemiological data, and research figures.
"""

from .transmission_plots import TransmissionPlotter
from .demographic_plots import DemographicPlotter
from .comparison_plots import ComparisonPlotter
from .figure_generators import FigureGenerator

__all__ = [
    "TransmissionPlotter",
    "DemographicPlotter", 
    "ComparisonPlotter",
    "FigureGenerator"
]
