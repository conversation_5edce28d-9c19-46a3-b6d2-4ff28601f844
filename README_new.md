# Intensification: Modernized Measles Transmission Modeling Package

A modernized Python 3.12+ package for analyzing measles transmission dynamics and vaccination campaign effectiveness in Southern Nigeria, based on the research paper:

**"Routine immunization intensification, vaccination campaigns, and measles transmission in Southern Nigeria"** (2025)

## Overview

This package provides a complete modernization of the original research codebase, implementing state-of-the-art software engineering practices while preserving the scientific methodology. The package enables researchers to:

- Model measles transmission using neighborhood SIR models with spatial correlation
- Analyze vaccination campaign effectiveness through survival analysis
- Process epidemiological surveillance data with case classification
- Estimate demographic parameters and birth seasonality
- Generate publication-quality visualizations and analysis reports

## Key Features

### 🔬 Scientific Capabilities
- **Stochastic Transmission Modeling**: Neighborhood SIR models with seasonal effects
- **Survival Analysis**: Vaccination coverage estimation across birth cohorts  
- **Case Classification**: Logistic regression for untested surveillance cases
- **Demographic Modeling**: Birth seasonality and population dynamics
- **Campaign Evaluation**: Quantitative assessment of vaccination interventions

### 🛠️ Modern Software Engineering
- **Python 3.12+**: Latest Python features and type hints
- **Modular Architecture**: Clean separation of concerns with well-defined APIs
- **Configuration Management**: Flexible configuration with Pydantic validation
- **Comprehensive Testing**: Unit and integration tests with pytest
- **Rich Logging**: Structured logging with rich console output
- **Documentation**: Complete API documentation and usage examples

### 📊 Data Processing
- **Automated Data Loading**: Centralized data management with validation
- **Time Series Processing**: Robust handling of epidemiological time series
- **Missing Data Handling**: Intelligent imputation and interpolation
- **Data Validation**: Comprehensive input validation and error handling

## Installation

### Requirements
- Python 3.12 or higher
- Dependencies managed via `pyproject.toml`

### Install from Source
```bash
git clone https://github.com/NThakkar-IDM/intensification.git
cd intensification
pip install -e .
```

### Development Installation
```bash
git clone https://github.com/NThakkar-IDM/intensification.git
cd intensification
pip install -e ".[dev,test,docs]"
```

## Quick Start

### Basic Usage
```python
from src.core import NeighborhoodSIRModel, Config
from src.data import DataLoader
from src.visualization import TransmissionPlotter

# Load configuration
config = Config()

# Load data for Lagos state
loader = DataLoader()
data = loader.load_all_state_data("lagos")

# Fit transmission model
model = NeighborhoodSIRModel()
results = model.fit(
    data=data['epidemiological'],
    sia_effects=data['vaccination'],
    s0_prior=1000,
    s0_var=100
)

# Generate visualizations
plotter = TransmissionPlotter()
fig = plotter.plot_transmission_dynamics(results)
```

### Command Line Interface
```bash
# Analyze a single state
intensification analyze --state lagos --output results/

# Generate all figures for a state
intensification plot --state lagos --figures all

# Run model validation
intensification validate --states all --forecast-years 3
```

## Package Structure

```
src/
├── core/                   # Core modeling classes
│   ├── transmission_model.py  # Neighborhood SIR model
│   ├── survival_analysis.py   # Vaccination coverage analysis
│   ├── base_model.py          # Abstract base classes
│   └── config.py              # Configuration management
├── data/                   # Data processing modules
│   ├── loaders.py             # Data loading utilities
│   ├── epidemiological.py     # Case data processing
│   ├── demographic.py         # Birth/population data
│   └── vaccination.py         # Vaccination data processing
├── models/                 # Specialized model implementations
│   ├── age_infection.py       # Age-at-infection models
│   ├── birth_seasonality.py   # Birth seasonality models
│   └── case_classification.py # Case classification models
├── visualization/          # Plotting and visualization
│   ├── transmission_plots.py  # Transmission model plots
│   ├── demographic_plots.py   # Demographic visualizations
│   └── figure_generators.py   # Publication figures
└── utils/                  # Utility functions
    ├── logging_config.py      # Logging setup
    ├── file_utils.py          # File operations
    ├── math_utils.py          # Mathematical utilities
    └── constants.py           # Package constants
```

## Research Methodology

This package implements the methodology described in the research paper:

### Transmission Model
- **Neighborhood SIR**: Stochastic transmission model with spatial correlation
- **Seasonality**: Periodic smoothing with correlation across geopolitical zones
- **Under-reporting**: Dynamic estimation of surveillance reporting rates
- **Vaccination Effects**: Explicit modeling of routine and campaign vaccination

### Survival Analysis
- **Cohort Tracking**: Follow birth cohorts through vaccination opportunities
- **Immunity Sources**: Partition immunity by vaccination vs. infection
- **Coverage Estimation**: Regression with post-stratification for survey data
- **Age-at-Infection**: Smooth estimation of infection age distributions

### Case Classification
- **Logistic Regression**: Classify untested clinically compatible cases
- **Temporal Correlation**: Account for time-varying confirmation rates
- **Feature Engineering**: Age, vaccination status, and temporal features
- **Validation**: Cross-validation against laboratory-confirmed cases

## Data Requirements

The package expects data in the `_data/` directory with the following files:

### Required Data Files
- `states_and_regions.csv`: State and geopolitical zone mapping
- `southern_states_epi_timeseries.csv`: Epidemiological surveillance data
- `monthly_births_by_state.csv`: Birth rate estimates by state
- `survey_mcv1_summary_stats.csv`: MCV1 coverage survey data
- `imputed_sia_calendar_by_state.csv`: Vaccination campaign calendar
- `southern_age_at_infection.csv`: Age-at-infection distributions
- `birth_seasonality_profiles.csv`: Seasonal birth patterns
- `grid3_population_by_state.csv`: Population estimates

### Data Format
All data files should be CSV format with standardized column names. See the `examples/` directory for sample data formats and validation scripts.

## Examples and Tutorials

### Example Scripts
- `examples/basic_analysis.py`: Basic transmission model fitting
- `examples/campaign_comparison.py`: Compare vaccination campaigns
- `examples/forecast_validation.py`: Out-of-sample forecasting
- `examples/sensitivity_analysis.py`: Parameter sensitivity testing

### Jupyter Notebooks
- `notebooks/tutorial_new.ipynb`: Complete package tutorial
- `notebooks/methodology_new.ipynb`: Research methodology walkthrough
- `notebooks/visualization_new.ipynb`: Plotting and visualization guide

## Configuration

The package uses Pydantic-based configuration management:

```python
from src.core import Config

# Load default configuration
config = Config()

# Load from file
config = Config.from_file("config.yaml")

# Customize settings
config.model.beta_correlation = 4.0
config.analysis.confidence_levels = [0.5, 0.8, 0.95]
```

## Testing

Run the test suite:
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test categories
pytest -m unit
pytest -m integration
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes following the coding standards
4. Add tests for new functionality
5. Run the test suite and ensure all tests pass
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

### Development Guidelines
- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive docstrings
- Include unit tests for new functionality
- Update documentation for API changes

## Citation

If you use this package in your research, please cite:

```bibtex
@article{thakkar2025intensification,
  title={Routine immunization intensification, vaccination campaigns, and measles transmission in Southern Nigeria},
  author={Thakkar, Niket and Oteri, Avuwa Joseph and McCarthy, Kevin},
  journal={medRxiv},
  year={2025},
  doi={10.1101/2025.02.24.25322796}
}
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Original research team: Niket Thakkar, Avuwa Joseph Oteri, Kevin McCarthy
- Nigeria CDC, US CDC, and NPHCDA for data collection and collaboration
- Bill & Melinda Gates Foundation for funding and support

## Support

For questions, issues, or contributions:
- Open an issue on GitHub
- Contact the development team
- Check the documentation and examples

---

**Note**: This is a modernized implementation of the original research codebase. The scientific methodology remains faithful to the published research while implementing modern software engineering practices for improved usability, maintainability, and extensibility.
