{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Intensification Package Tutorial\n", "\n", "This notebook provides a comprehensive tutorial for the modernized intensification package for measles transmission modeling.\n", "\n", "## Overview\n", "\n", "The intensification package implements state-of-the-art methods for analyzing measles transmission dynamics and vaccination campaign effectiveness, based on the research paper:\n", "\n", "**\"Routine immunization intensification, vaccination campaigns, and measles transmission in Southern Nigeria\"** (2025)\n", "\n", "### What you'll learn:\n", "1. Package installation and setup\n", "2. Data loading and validation\n", "3. Transmission model fitting\n", "4. Vaccination campaign analysis\n", "5. Visualization and interpretation\n", "6. Model validation and forecasting"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Installation\n", "\n", "First, let's import the necessary packages and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Standard imports\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "\n", "# Add src to path\n", "sys.path.insert(0, str(Path.cwd().parent))\n", "\n", "# Package imports\n", "from src.core import NeighborhoodSIRModel, SurvivalAnalyzer, Config\n", "from src.data import DataLoader, EpidemiologicalDataProcessor\n", "from src.utils import setup_logging, get_logger\n", "from src.utils.constants import STATES_SOUTHERN_NIGERIA, COLORS\n", "\n", "# Setup\n", "setup_logging(level=\"INFO\", use_rich=True)\n", "logger = get_logger(__name__)\n", "\n", "# Plotting setup\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(COLORS)\n", "\n", "print(\"✅ Package imported successfully!\")\n", "print(f\"Available states: {len(STATES_SOUTHERN_NIGERIA)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration and Data Loading\n", "\n", "The package uses a configuration system to manage parameters and data paths."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load configuration\n", "config = Config()\n", "\n", "# Display configuration\n", "print(\"Configuration loaded:\")\n", "print(f\"Data directory: {config.data_paths.data_dir}\")\n", "print(f\"Default state: {config.analysis.default_state}\")\n", "print(f\"Analysis period: {config.analysis.analysis_start_year}-{config.analysis.analysis_end_year}\")\n", "print(f\"Model parameters: β_corr={config.model.beta_correlation}, τ={config.model.tau}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize data loader\n", "data_loader = DataLoader()\n", "\n", "# Check data availability\n", "print(\"Checking data availability...\")\n", "availability = data_loader.validate_data_availability(['lagos', 'oyo', 'rivers'])\n", "\n", "for state, available in availability.items():\n", "    status = \"✅\" if available else \"❌\"\n", "    print(f\"{status} {state.upper()}: {'Available' if available else 'Not available'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Exploration\n", "\n", "Let's explore the data structure and content for Lagos state."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get data summary\n", "summary = data_loader.get_data_summary()\n", "\n", "print(\"Data Summary:\")\n", "print(\"=\" * 50)\n", "\n", "for data_type, info in summary.items():\n", "    print(f\"\\n{data_type.upper()}:\")\n", "    if 'error' in info:\n", "        print(f\"  ❌ Error: {info['error']}\")\n", "    else:\n", "        print(f\"  📊 Records: {info.get('total_records', 'N/A')}\")\n", "        if 'date_range' in info and info['date_range']:\n", "            print(f\"  📅 Date range: {info['date_range']['start']} to {info['date_range']['end']}\")\n", "        if 'columns' in info:\n", "            print(f\"  📋 Columns: {', '.join(info['columns'][:5])}{'...' if len(info['columns']) > 5 else ''}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}